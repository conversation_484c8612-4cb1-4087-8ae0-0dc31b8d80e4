# Generated by Django 4.2.5 on 2025-06-24 06:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0220_appointmentreminder'),
    ]

    operations = [
        migrations.RenameField(
            model_name='appointmentreminder',
            old_name='email_sent',
            new_name='followup_email_sent',
        ),
        migrations.RenameField(
            model_name='appointmentreminder',
            old_name='notification_sent',
            new_name='followup_notification_sent',
        ),
        migrations.RemoveField(
            model_name='appointmentreminder',
            name='reminder_sent_at',
        ),
        migrations.AddField(
            model_name='appointmentreminder',
            name='followup_reminder_sent_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='appointmentreminder',
            name='immediate_email_sent',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='appointmentreminder',
            name='immediate_notification_sent',
            field=models.<PERSON><PERSON>anField(default=False),
        ),
        migrations.AddField(
            model_name='appointmentreminder',
            name='immediate_reminder_sent_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
