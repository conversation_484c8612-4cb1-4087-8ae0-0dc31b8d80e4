# services/email_service.py
import os
import json
import requests
from requests.exceptions import HTT<PERSON><PERSON>rror, RequestException
from django.conf import settings
import logging
from pathlib import Path
from django.contrib.auth import get_user_model
from cu_app.models.patient_models import CuUser
logger = logging.getLogger(__name__)


class EmailService:
    """
    Robust email service using ZeptoMail API for sending templated emails.
    Handles different notification scenarios for patients and doctors.
    """

    def __init__(self):

        self.zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
        self.base_headers = {
            "Authorization": settings.ZEPTOMAIL_TOKEN,
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        self.default_sender = {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        }
        self.social_media = {
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        }
        self.templates = self._load_template_keys()

    def _load_template_keys(self):
        """Load template keys from JSON config file"""
        try:
            config_path = Path(__file__).resolve().parent.parent / \
                'config' / 'email_templates.json'
            with open(config_path) as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error("Email templates config file not found!")
            return {}
        except json.JSONDecodeError:
            logger.error("Invalid JSON in email templates config!")
            return {}

    def _send_email(self, payload):
        """
        Internal method to actually send the email via ZeptoMail API
        """
        try:
            response = requests.post(
                self.zeptomail_url,
                headers=self.base_headers,
                data=json.dumps(payload),
                timeout=10  # Add timeout to prevent hanging
            )
            response.raise_for_status()  # Raises HTTPError for bad responses

            logger.info(
                f"Status from email : {response.status_code}, "
                f"Response from email: {response.content}"
            )

            # Log successful sending (consider using Django's logging)
            print(f"Email sent successfully - Status: {response.status_code}")
            return True

        except HTTPError as e:
            logger.error(f"Invalid email configuration: {str(e)}")
            # Consider adding retry logic here for certain status codes
            return False
        except RequestException as e:
            logger.error(f"Failed to send email: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error while sending email: {str(e)}")
            return False

    def notify_admin_email(self, template_key_name, custom_fields):
        try:
            template_key = self.templates[template_key_name]
        except KeyError:
            logger.error(
                f"Template key '{template_key_name}' not found in email_templates.json")
            return False

        # Merge with social media links
        all_fields = dict(self.social_media)
        all_fields.update(custom_fields)

        # Fetch all admin users (including child admins)
        # admin_users = get_user_model().objects.filter(role__in=["admin", "child_admin"])
        admin_users = CuUser.objects.filter(is_admin__exact=1)

        if not admin_users.exists():
            logger.warning("No admin users found to notify.")
            return False

        success = True
        for admin in admin_users:
            try:
                payload = {
                    "template_key": template_key,
                    "from": self.default_sender,
                    "to": [{
                        "email_address": {
                            "address": admin.email,
                            "name": admin.name,
                        }
                    }],
                    "merge_info": all_fields
                }
                result = self._send_email(payload)
                if not result:
                    success = False
            except Exception as e:
                logger.error(
                    f"Failed to send admin email to {admin.email}: {e}")
                success = False

        return success

    def _build_base_payload(self, template_key, recipient_email, custom_fields):
        all_fields = dict(self.social_media)
        all_fields.update(custom_fields)

        return {
            "template_key": template_key,
            "from": self.default_sender,
            "to": [{
                "email_address": {
                    "address": recipient_email,
                    "name": recipient_email,
                }
            }],
            "merge_info": all_fields
        }

    def replied_query_email(self, user_email, user_name, app_url):
        """Send reply notification email"""
        try:
            template_key = self.templates['REPLY_QUERY_KEY']
        except KeyError:
            logger.error("Reply notification template key not found!")
            return False

        custom_fields = {
            "u_name": user_name,
            "link": app_url
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)

    def reply_patient_query_email(self, user_email, user_name, app_url, patient_name,amount):
        """Send reply notification email"""
        try:
            template_key = self.templates['REPLY_PATIENT_QUERY_KEY']
        except KeyError:
            logger.error("Reply notification template key not found!")
            return False

        custom_fields = {
            "u_name": user_name,
            "link": app_url,
            "patient_name": patient_name,
            "amount":amount
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)

    def subscription_email(self, user_email, user_name, app_url):
        """Send reply notification email"""
        try:
            template_key = self.templates['JOIN_COMMUNITY_TEMPLATE_KEY']
        except KeyError:
            logger.error("Reply notification template key not found!")
            return False

        custom_fields = {
            "u_email": user_email,
            "u_name": user_name,
            "verify_url1": app_url
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)

    def onboarding_email(self, user_email, user_name, app_url, user=None):
        """Send reply notification email"""

        if user == 'expert':
            template_key_name = 'EXPERT_ONBOARDING_TEMPLATE_KEY'
        elif user == 'patient':
            template_key_name = 'PATIENT_ONBOARDING_TEMPLATE_KEY'
        else:
            template_key_name = 'PATIENT_ONBOARDING_TEMPLATE_KEY'

        try:
            template_key = self.templates[template_key_name]
        except KeyError:
            logger.error("Onboarding template key not found!")
            return False

        custom_fields = {
            "u_email": user_email,
            "u_name": user_name,
            "verify_url1": app_url
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)

    def autocredit_no_reply_email(self, user_email, user_name, app_url,amount):
        """Send reply notification email"""
        try:
            template_key = self.templates['AUTOCREDIT_NO_REPLY_TEMPLATE_KEY']
        except KeyError:
            logger.error("Reply notification template key not found!")
            return False

        custom_fields = {
            "u_email": user_email,
            "u_name": user_name,
            "verify_url1": app_url,
            "amount":amount
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)


#  """PAYMENTS"""

#  """CANCELLATIONS"""

    def cancellation_email_patient(self, user_email, user_name, app_url, patient_name, appointment_timings):
        """APPOINTMENT CANCELLATION  REQUEST"""
        try:
            template_key = self.templates['APPOINTMENT_CANCELLATION_PATIENT']
        except KeyError:
            logger.error("This Email Template key not found!")
            return False

        custom_fields = {
            "u_name": user_name,
            "link": app_url,
            "patient_name": patient_name,
            "appointment_timings": appointment_timings,
            "user_role": "patient",
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)

    def cancellation_email_expert(self, user_email, user_name, app_url, patient_name, appointment_timings, expert_role):
        try:
            template_key = self.templates['APPOINTMENT_CANCELLATION_EXPERT']
        except KeyError:
            logger.error("This Email Template key not found!")
            return False

        custom_fields = {
            "u_name": user_name,
            "link": app_url,
            "patient_name": patient_name,
            "appointment_timings": appointment_timings,
            "user_role": expert_role,
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)

    def cancellation_rejected_email_patient(self, user_email, user_name, rejection_reason, app_url):

        try:
            template_key = self.templates['APPOINTMENT_CANCELLATION_REJECTED']
        except KeyError:
            logger.error("This Email Template key not found!")
            return False

        custom_fields = {
            "u_name": user_name,
            "link": app_url,
            "patient_name": user_name,
            "rejection_reason": rejection_reason,
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)

  #  """RESCHEDULE"""
    def reschedule_email_patient(self, user_email, user_name, app_url, appointment_timings, expert_name, app_id, prev_appointment_timings):
        try:
            template_key = self.templates['APPOINTMENT_RESCHEDULE_PATIENT']
        except KeyError:
            logger.error("This Email Template key not found!")
            return False

        custom_fields = {
            "aa_name": user_name,
            "link": app_url,
            "a_name": expert_name,
            "prev_timings": prev_appointment_timings,
            "new_timings": appointment_timings,
            "app_id": app_id,
            "user_role": "patient",
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)

    def reschedule_email_expert(self, user_email, user_name, app_url, patient_name, appointment_timings, expert_role, app_id, prev_appointment_timings):
        try:
            template_key = self.templates['APPOINTMENT_RESCHEDULE_DOCTOR']
        except KeyError:
            logger.error("This Email Template key not found!")
            return False

        custom_fields = {
            "aa_name": user_name,
            "link": app_url,
            "a_name": patient_name,
            "prev_timings":  prev_appointment_timings,
            "new_timings": appointment_timings,
            "app_id": app_id,
            "user_role": expert_role,
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)

    def refund_email_patient(self, user_email, amount, patient_name, doctor_name, appointment_id, appointment_timings):
        try:
            template_key = self.templates['REFUND_PATIENT']
        except KeyError:
            logger.error("This Email Template key not found!")
            return False

        custom_fields = {
            "expert_name": doctor_name,
            "p_name": patient_name,
            "app_timings": appointment_timings,
            "app_id": appointment_id,
            "amount": amount,
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)

    def ask_query_reminder_patient(self, user_email, patient_name, doctor_name, patient_url, appointment_timings):
        try:
            template_key = self.templates['REMINDER_FOR_PATIENT_ASK_QUERY']
        except KeyError:
            logger.error("This Email Template key not found!")
            return False

        custom_fields = {
            "expert_name": doctor_name,
            "patient_name": patient_name,
            "patient_url": patient_url,
            "appointment_timings": appointment_timings
        }

        payload = self._build_base_payload(
            template_key, user_email, custom_fields)
        return self._send_email(payload)


    def send_expert_query_reminder(self,expert_email,expert_name,patient_name,query_created,query_text,remaining_hours,query_url,reminder_type):
        try:
            template_key = self.templates['REMINDER_FOR_EXPERT_REPLY_QUERY']
        except KeyError:
            logger.error("This Email Template key not found!")
            return False

        custom_fields = {
            "expert_name": expert_name,
            "patient_name": patient_name,
            "patient_url": query_url
        }

        payload = self._build_base_payload(
            template_key, expert_email, custom_fields)
        return self._send_email(payload)
