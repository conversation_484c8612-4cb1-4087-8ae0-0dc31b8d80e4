from django.db import models
from django.contrib.auth.models import BaseUser<PERSON>anager, AbstractBaseUser, PermissionsMixin
from datetime import *
from rest_framework import generics, status
from rest_framework.response import Response
from django.http import JsonResponse
from django.utils import timezone

# Create your models here.


class ExpertiseCancertype(models.Model):
    name = models.CharField(unique=True, max_length=50)
    type = models.CharField(null=True, blank=True, max_length=50)
    #add
    embedding = models.JSONField(null=True, blank=True)


class CuUserManager(BaseUserManager):
    def create_user(self, name, phone, email, password=None, country_code=None):
        """
        Creates and saves a User with the given email, date of
        birth and password.
        """
        if not email:
            raise ValueError("Users must have an email address")
        try:
            user = self.model(
                email=self.normalize_email(email),
                name=name,
                phone=phone,
                country_code=country_code,
                approval="Approved"

            )

            user.set_password(password)
            user.save(using=self._db)
            return user
        except:
            return JsonResponse({"status": "failed"})

    def create_superuser(self, email, date_of_birth, password=None):
        """
        Creates and saves a superuser with the given email, date of
        birth and password.
        """
        user = self.create_user(
            email,
            password=password,
            date_of_birth=date_of_birth,
        )
        user.is_admin = True
        user.save(using=self._db)
        return user


class CuUser(AbstractBaseUser, PermissionsMixin):
    email = models.EmailField(
        verbose_name="email address",
        max_length=255,
        unique=True,
    )
    # date_of_birth = models.DateField(default=date.fromisoformat('2019-12-04'))
    date_of_birth = models.DateField(blank=True, null=True)
    name = models.TextField(blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True, unique=True)
    approval = models.CharField(max_length=20, default="Approved")
    is_active = models.BooleanField(default=True)
    is_admin = models.BooleanField(default=False)
    sex = models.CharField(max_length=20, blank=True, null=True)
    age = models.CharField(max_length=20, blank=True, null=True)
    # cancer_type=models.CharField(max_length=200, blank=True, null=True)
    cancer_type = models.JSONField(null=True)
    expertise = models.ManyToManyField(ExpertiseCancertype, blank=True)
    DateOfRegistration = models.DateTimeField(auto_now_add=True)
    TimeZone = models.CharField(max_length=100, default="UTC")
    PWVerifyCode = models.CharField(max_length=100, blank=True, null=True)
    PWCodeGentime = models.DateTimeField(auto_now=True)
    City = models.CharField(max_length=100, blank=True, null=True)
    Country = models.CharField(max_length=100, blank=True, null=True)
    country_code = models.TextField(blank=True, null=True)
    prefix = models.CharField(max_length=5, blank=True, null=True)

    # embedding = models.JSONField(null=True, blank=True)
    objects = CuUserManager()

    USERNAME_FIELD = "email"

    # REQUIRED_FIELDS = ["date_of_birth"]

    def __str__(self):
        return self.email

    def has_perm(self, perm, obj=None):
        "Does the user have a specific permission?"
        # Simplest possible answer: Yes, always
        return True

    def has_module_perms(self, app_label):
        "Does the user have permissions to view the app `app_label`?"
        # Simplest possible answer: Yes, always
        return True

    @property
    def is_staff(self):
        "Is the user a member of staff?"
        # Simplest possible answer: All admins are staff
        return self.is_admin

class Embedding(models.Model):
    user = models.OneToOneField('CuUser', on_delete=models.CASCADE, related_name='embedding')
    embedding = models.JSONField()
    embedding_type = models.CharField(max_length=100, default="doctor_profile")  # Optional: useful for multiple embeddings
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class cu_permissions(models.Model):
    PermName = models.CharField(null=True, blank=True, max_length=50)


class patient_medical_records(models.Model):
    reportname = models.CharField(max_length=50)
    reporttype = models.CharField(max_length=50)
    generation_date = models.DateTimeField(max_length=50)
    reportsummary = models.TextField(blank=True, null=True)
    report_file = models.JSONField(blank=True, null=True)
    patient = models.ForeignKey(CuUser, on_delete=models.CASCADE)


class SchedulerSlots(models.Model):
    schedule_start_time = models.DateTimeField()
    schedule_end_time = models.DateTimeField()
    doctor = models.ForeignKey(CuUser, on_delete=models.CASCADE)
    timezone = models.CharField(max_length=50, default='UTC')
    status = models.CharField(max_length=2, null=True, blank=True)


class Appointments(models.Model):
    slot_id = models.OneToOneField(SchedulerSlots, on_delete=models.CASCADE)
    patient = models.ForeignKey(CuUser, on_delete=models.CASCADE)
    app_statuses = [
        ("P", "Pending"),
        ("B", "Booked"),
        ("C_P", "Cancellation Pending"),
        ("C_R", "Cancellation Rejected"),
        ("C", "Cancelled"),
        ("R", "Rescheduled")
    ]
    status = models.CharField(max_length=4,
                              choices=app_statuses, default="P")
    summary = models.TextField(blank=True, null=True)
    location = models.CharField(max_length=50)
    description = models.TextField(blank=True, null=True)
    DateOfFixingApp = models.DateField(default=date.today)
    Invoice = models.TextField(blank=True, null=True)

class AppointmentReminder(models.Model):
    appointment = models.OneToOneField('Appointments', on_delete=models.CASCADE)
    immediate_reminder_sent_at = models.DateTimeField(null=True, blank=True)
    immediate_notification_sent = models.BooleanField(default=False)
    immediate_email_sent = models.BooleanField(default=False)
    followup_reminder_sent_at = models.DateTimeField(null=True, blank=True)
    followup_notification_sent = models.BooleanField(default=False)
    followup_email_sent = models.BooleanField(default=False)
    # reminder_sent_at = models.DateTimeField(null=True)
    # email_sent = models.BooleanField(default=False)
    # notification_sent = models.BooleanField(default=False)


class AppointmentMgmt(models.Model):
    AppId = models.ForeignKey(Appointments, on_delete=models.CASCADE)
    EventType = models.CharField(max_length=50, null=True, blank=True)
    By = models.ForeignKey(CuUser, on_delete=models.CASCADE)
    EventDate = models.DateTimeField(default=timezone.now)
    AppointmentReason = models.TextField(blank=True, null=True)
    RejectionReason = models.TextField(blank=True, null=True)


class AppointmentConsent(models.Model):
    AppId = models.ForeignKey(Appointments, blank=True,
                              null=True, on_delete=models.CASCADE)
    PatientConsent = models.IntegerField(default=0)
    ExpertConsent = models.IntegerField(default=0)
    PatientConsent_DoctorForm = models.IntegerField(default=0)


class PatientDetails(models.Model):
    PatientId = models.OneToOneField(
        CuUser,
        on_delete=models.CASCADE
    )
    Address = models.TextField(blank=True, null=True)

    Height = models.CharField(max_length=50, blank=True, null=True)
    Weight = models.CharField(max_length=50, blank=True, null=True)
    DietaryRestrictions = models.TextField(blank=True, null=True)
    Allergies = models.CharField(max_length=100, blank=True, null=True)
    ExistingIllness = models.TextField(blank=True, null=True)
    PastIllness = models.TextField(blank=True, null=True)
    Notes = models.TextField(blank=True, null=True)
    # TimeZone = models.CharField(max_length=100, blank=True, null=True)
    ProfilePhoto = models.TextField(blank=True, null=True)
    EmailVerifyCode = models.CharField(max_length=100, blank=True, null=True)
    EmailVerified = models.BooleanField(default=False)
    EmailCodeGentime = models.DateTimeField(auto_now=True)
    NDAConsent = models.IntegerField(default=0)
    Signature = models.TextField(blank=True, null=True)
    SocialLogin = models.IntegerField(default=0)


class PatientQueries(models.Model):
    STATUS_CHOICES = [
    ('P', 'Pending'),      # Query raised but not replied
    ('R', 'Replied'),      # Replied
    ('C', 'Closed'),       # Marked as resolved/closed (optional future state)
    ]
    ApptId = models.ForeignKey(
        Appointments, blank=True, null=True, on_delete=models.CASCADE)
    PQuery = models.TextField(blank=True, null=True)
    ReplyTo = models.IntegerField(blank=True, null=True)
    QueryTime = models.DateTimeField(default=timezone.now)

    first_reminder_sent = models.DateTimeField(null=True, blank=True)
    second_reminder_sent = models.DateTimeField(null=True, blank=True)
    final_reminder_sent = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=1, choices=STATUS_CHOICES, default='P')
    # status = models.CharField(max_length=2, default='P')  #A if answered


class PatientStories(models.Model):
    ApptId = models.ForeignKey(
        Appointments, blank=True, null=True, on_delete=models.CASCADE)
    DoctorRecommendation = models.IntegerField(blank=True, null=True)
    CancerTreatmentType = models.CharField(
        max_length=100, blank=True, null=True)
    WaitingTime = models.CharField(max_length=100, blank=True, null=True)
    Improvement = models.CharField(max_length=100, blank=True, null=True)
    Rating = models.IntegerField(blank=True, null=True)
    ExperienceSummary = models.TextField(blank=True, null=True)
    Anonymousity = models.IntegerField(blank=True, null=True)
    status = models.IntegerField(default=2)
    CurrentTime = models.DateTimeField(auto_now_add=True)


# added
class ReportType(models.Model):
    type = models.CharField(unique=True, max_length=50)

