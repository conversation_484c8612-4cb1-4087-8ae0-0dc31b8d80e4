# management/commands/send_post_appointment_reminders.py
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from cu_app.utils.helper_functions import *
from cu_admin.user_notifications import *
import os
from cu_app.services.email_services import EmailService
import logging
from cu_app.models.patient_models import *
from django.db.models import Q

logger = logging.getLogger(__name__)

email_service = EmailService()

class Command(BaseCommand):
    help = 'Sends post-appointment reminders (immediate 10-min and 24-hour follow-ups)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reminder-type',
            type=str,
            choices=['immediate', 'follow-up', 'all'],
            default='all',
            help='Type of reminder to send (immediate: 10-min, follow-up: 24-hour)'
        )

    def handle(self, *args, **options):
        reminder_type = options['reminder_type']
        
        if reminder_type in ['immediate', 'all']:
            self._process_immediate_reminders()
        
        if reminder_type in ['follow-up', 'all']:
            self._process_followup_reminders()

    def _process_immediate_reminders(self):
        """Process reminders for 10 minutes after appointment completion"""
        time_window_start = timezone.now() - timedelta(minutes=15)  # Buffer window
        time_window_end = timezone.now() - timedelta(minutes=10)

        logger.info(f"No TIme {timezone.now()} ")
        logger.info(f"Processing immediate reminders for appointments between {time_window_start} and {time_window_end}")

        try:
            completed_appointments = self._get_completed_appointments_in_window(
                time_window_start, time_window_end
            )

            # Filter out appointments that already have queries or immediate reminders
            appointments_with_queries = set(
                PatientQueries.objects.filter(
                    ApptId__in=completed_appointments
                ).values_list('ApptId', flat=True)
            )

            appointments_with_reminders = set(
                AppointmentReminder.objects.filter(
                    appointment__in=completed_appointments,
                    immediate_reminder_sent_at__isnull=False
                ).values_list('appointment', flat=True)
            )

            processed_count = 0

            for appointment in completed_appointments:
                if (appointment.id in appointments_with_queries or
                        appointment.id in appointments_with_reminders):
                    continue

                self._send_reminder(appointment, reminder_type='immediate')
                processed_count += 1

            logger.info(f"Successfully sent {processed_count} immediate reminders")
            self.stdout.write(self.style.SUCCESS(
                f"Sent {processed_count} immediate reminders"))

        except Exception as e:
            logger.error(f"Error in immediate reminder processing: {str(e)}")
            self.stdout.write(self.style.ERROR(f'Error: {str(e)}'))

    def _process_followup_reminders(self):
        """Process reminders for 24 hours after appointment completion"""
        # ideal_time = timezone.now() - timedelta(hours=36)
        # time_window_start = ideal_time - timedelta(hours=6)
        # time_window_end = ideal_time + timedelta(hours=6)
        time_threshold = timezone.now() - timedelta(hours=36)
        logger.info(f"Processing follow-up reminders for appointments completed before {time_threshold}")
        

        logger.info(f"No Time {timezone.now()} ")

        try:
            # completed_appointments = self._get_completed_appointments_in_window(
            #     time_window_start, time_window_end
            # )
            completed_appointments = Appointments.objects.filter(
                Q(status='B') | Q(status='C_R') | Q(status='R'),
                slot_id__schedule_end_time__lte=time_threshold,
                meetingsession__MeetingStatus=2
            ).select_related('patient', 'slot_id__doctor')

            # Filter out appointments that already have queries or follow-up reminders
            appointments_with_queries = set(
                PatientQueries.objects.filter(
                    ApptId__in=completed_appointments
                ).values_list('ApptId', flat=True)
            )

            appointments_with_reminders = set(
                AppointmentReminder.objects.filter(
                    appointment__in=completed_appointments,
                    followup_reminder_sent_at__isnull=False
                ).values_list('appointment', flat=True)
            )

            processed_count = 0

            for appointment in completed_appointments:
                if (appointment.id in appointments_with_queries or
                        appointment.id in appointments_with_reminders):
                    continue

                self._send_reminder(appointment, reminder_type='follow-up')
                processed_count += 1

            logger.info(f"Successfully sent {processed_count} follow-up reminders")

        except Exception as e:
            logger.error(f"Error in follow-up reminder processing: {str(e)}")
            self.stdout.write(self.style.ERROR(f'Error: {str(e)}'))

    def _get_completed_appointments_in_window(self, start_time, end_time):
        """Shared method to get completed appointments in time window"""
        return Appointments.objects.filter(
            Q(status='B') | Q(status='C_R') | Q(status='R'),
            slot_id__schedule_end_time__range=(start_time, end_time),
            meetingsession__MeetingStatus=2
        ).select_related('patient', 'slot_id__doctor')

    def _send_reminder(self, appointment, reminder_type):
        """Handles the actual reminder sending with tracking"""
        reminder, created = AppointmentReminder.objects.get_or_create(
            appointment=appointment
        )
        patient = appointment.patient
        doctor = appointment.slot_id.doctor

        slot_details = get_appointment_details(
            slot_id=appointment.slot_id_id,
            doctor_id=doctor.id
        )
        appointment_timings = slot_details["appointment_timings"]
        patient_url = os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments'

        try:
            if reminder_type == 'immediate':
                if reminder.immediate_reminder_sent_at:
                    return  # Already sent

                if notification_check("Auto credited"):
                    send_generic_push_notification(
                        user_id=patient.id,
                        title="Appointment Completed",
                        body=f"Your appointment with Dr. {doctor.name} has ended. You can now ask follow-up questions",
                        link=patient_url
                    )
                    reminder.immediate_notification_sent = True

                    if email_check("Appointment Completed"):
                        email_service.ask_query_reminder_patient(
                            patient.email, patient.name, doctor.name, patient_url, appointment_timings
                        )
                        reminder.immediate_email_sent = True

                reminder.immediate_reminder_sent_at = timezone.now()

            elif reminder_type == 'follow-up':
                if reminder.followup_reminder_sent_at:
                    return  # Already sent

                if notification_check("Auto credited"):
                    send_generic_push_notification(
                        user_id=patient.id,
                        title="Follow-up Available",
                        body=f"Don't forget to ask follow-up questions about your appointment with Dr. {doctor.name}",
                        link=patient_url
                    )
                    reminder.followup_notification_sent = True

                    if email_check("Follow-up Available"):
                        email_service.ask_query_reminder_patient(
                            patient.email, patient.name, doctor.name, patient_url, appointment_timings
                        )
                        reminder.followup_email_sent = True

                reminder.followup_reminder_sent_at = timezone.now()

            reminder.save()
            logger.info(f"Successfully processed {reminder_type} reminder for appointment {appointment.id}")

        except Exception as e:
            logger.error(f"Failed to send {reminder_type} reminder for appointment {appointment.id}: {str(e)}", exc_info=True)