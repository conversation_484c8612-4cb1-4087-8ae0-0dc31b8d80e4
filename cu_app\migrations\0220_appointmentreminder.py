# Generated by Django 4.2.5 on 2025-06-20 11:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cu_app', '0219_appointmentmgmt_rejectionreason'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppointmentReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reminder_sent_at', models.DateTimeField(null=True)),
                ('email_sent', models.BooleanField(default=False)),
                ('notification_sent', models.BooleanField(default=False)),
                ('appointment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='cu_app.appointments')),
            ],
        ),
    ]
