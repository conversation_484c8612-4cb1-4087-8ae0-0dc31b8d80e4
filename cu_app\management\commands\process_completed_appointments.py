from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import os
from django.db import transaction

from cu_app.models.misc_models import *
from cu_app.models.patient_models import *
from cu_admin.user_notifications import *

from django.utils.timezone import localtime
import logging
from cu_app.models.doctor_models import *
from django.db.models import Max
from cu_app.services.email_services import EmailService
from cu_admin.user_notifications import *
from django.db.models import Q


email_service = EmailService()


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Transfer expert payments for completed appointments (MeetingStatus=2  means completed) with no queries within 3 days of appointment completion'

    def handle(self, *args, **kwargs):
        current_time = timezone.now()
        time_threshold = current_time - timedelta(days=3)
        processed_count = 0

        try:
            # Get all completed appointments that qualify for payment processings
            completed_appointments = Appointments.objects.filter(
                Q(status='B') | Q(status='C_R') | Q(status='R'),
                slot_id__schedule_end_time__lte=time_threshold,
                meetingsession__MeetingStatus=2
            ).select_related(
                'slot_id__doctor__doctordetails', 'patient'
            ).prefetch_related(
                'patientpayment_set', 'meetingsession_set'
            )
            # Get all appointments that have queries in a single query
            appointments_with_queries = set(
                PatientQueries.objects.filter(
                    ApptId__in=completed_appointments
                ).values_list('ApptId', flat=True)
            )

            # Get all appointments that already have transactions in a single query
            appointments_with_transactions = set(
                ExpertWalletTransactions.objects.filter(
                    appointment__in=completed_appointments,
                    TransactionType=0
                ).values_list('appointment', flat=True)
            )

            # Get all wallet IDs that will be processed
            wallet_ids = ExpertWallet.objects.filter(
                ExpertId__in=completed_appointments.values('slot_id__doctor')
            ).values_list('id', flat=True)

            # Get the latest balance for each wallet in one query
            latest_balances = {
                wallet_id: balance for wallet_id, balance in
                ExpertWalletTransactions.objects.filter(
                    WalletId__in=wallet_ids
                ).values('WalletId').annotate(
                    latest_balance=Max('BalanceAmount')
                ).values_list('WalletId', 'latest_balance')
            }

            # Initialize wallet balances with the latest balances or 0 if no transactions exist
            wallet_balances = {
                wallet_id: latest_balances.get(wallet_id, 0)
                for wallet_id in wallet_ids
            }

            for appointment in completed_appointments:
                if (appointment.id in appointments_with_queries or
                        appointment.id in appointments_with_transactions):
                    continue

                payment = appointment.patientpayment_set.first()
                if not payment:
                    logger.warning(
                        f"No payment found for appointment {appointment.id}")
                    continue

                try:
                    with transaction.atomic():
                        doctor = appointment.slot_id.doctor

                        # Get commission percentage
                        commission_percentage = getattr(
                            getattr(doctor, "doctordetails", None),
                            "CommissionPercentage",
                            0
                        ) or 0

                        commission_amount = int(
                            (payment.amount * commission_percentage) / 100)
                        credit_amount = payment.amount - commission_amount

                        # Get or create wallet
                        expert_wallet, _ = ExpertWallet.objects.get_or_create(
                            ExpertId=doctor)

                        # Get current balance (initialize to 0 if first transaction)
                        current_balance = wallet_balances.get(
                            expert_wallet.id, 0)
                        new_balance = current_balance + credit_amount

                        # Create transaction
                        ExpertWalletTransactions.objects.create(
                            WalletId=expert_wallet,
                            TransactionDate=current_time,
                            TransactionType=0,  # Credit
                            TransactionAmount=credit_amount,
                            CommissionAmount=commission_amount,
                            BalanceAmount=new_balance,
                            appointment=appointment
                        )

                        # Update the wallet balance for subsequent transactions
                        wallet_balances[expert_wallet.id] = new_balance

                        # Send notifications
                        user = appointment.patient
                        doctor = appointment.slot_id.doctor
                        if notification_check("Auto credited"):
                            app_url = os.getenv(
                                "NEXT_CANCER_UNWIRED_DOCTOR_APP", "") + "/payments"
                            send_generic_push_notification(
                                doctor.id, "Auto credited", f"Your Wallet is credited for the appointment id {appointment.id} with user {user.name}", app_url)
                 
                        if email_check("Auto credited"):
                            app_url = os.getenv(
                                "NEXT_CANCER_UNWIRED_DOCTOR_APP", "") + "/payments"
                            email_service.autocredit_no_reply_email(
                                doctor.email, doctor.name, app_url,payment.amount)

                        processed_count += 1
                        logger.info(
                            f"Processed payment for appointment {appointment.id}")

                except Exception as e:
                    logger.error(
                        f"Error processing appointment {appointment.id}: {str(e)}")
                    continue

            logger.info(
                f'Successfully processed {processed_count} expert payments')
            self.stdout.write(self.style.SUCCESS(
                f'Successfully processed {processed_count} expert payments'))

        except Exception as e:
            logger.error(f"Error in payment transfer cron job: {str(e)}")
            self.stdout.write(self.style.ERROR(f'Error: {str(e)}'))
