from __future__ import print_function
from django.views.generic import TemplateView
from django.core.cache import cache
from django.db.models import Prefetch
from rest_framework import status
from rest_framework.views import APIView
import uuid
from django.http import JsonResponse
from rest_framework import views
from rest_framework import generics, status, views
from django.http import JsonResponse, HttpResponse
from cu_admin.user_notifications import *
from cu_app.utils.sementic_search import get_embedding, build_detailed_doctor_data
from cu_app.utils.helper_functions import *
from cu_app.services.airwallex import AirwallexService
from cu_app.services.email_services import EmailService
from ..serializers import *
from datetime import *
from rest_framework.response import Response
import json
from django.contrib.auth.models import Group, Permission
from django.views import View
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import authenticate, login, logout, get_user_model
from ..models import *
import os
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import os.path
from rest_framework.permissions import AllowAny

import re

# from datetime import datetime,date, timedelta,timezone
from django.conf import settings
from django.core.files.storage import FileSystemStorage
from ..forms import *
from ..cu_library import *

# aws sns
import logging
import time
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv
from rest_framework.parsers import JSONParser
import json
import requests
from django.utils import timezone
import zoneinfo
from django.utils.dateparse import parse_datetime
import stripe
import base64
from django.shortcuts import redirect

# fcm django
from firebase_admin.messaging import (
    Message,
    Notification,
    WebpushConfig,
    WebpushFCMOptions,
)
from fcm_django.models import FCMDevice
from django.db.models import Q
from django.core.paginator import Paginator
from ..views.scheduler_views import CreateOrder, FulfillOrder
# from cu_admin.views.views1 import AdminApprovePushNotification

from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

from cu_app.utils import *

ENV_ROOT = os.path.join(settings.BASE_DIR, ".env")
load_dotenv(ENV_ROOT)


email_service = EmailService()

logger = logging.getLogger(__name__)


def get_cu_user(a):
    return get_user_model().objects.get(id=a)


def filter_user_data(a):
    keys = [
        "is_admin",
        "is_active",
        "is_superuser",
        "password",
        "user_permissions",
        "groups",
        "PWVerifyCode",
        "PWCodeGentime",
    ]
    result = {
        k: v for k, v in CuUserRegisterSerializer(a).data.items() if k not in keys
    }
    print(f"in filter user data   {a.groups.all()}")
    result["role"] = a.groups.all()[0].name
    if (
        result["role"] == "doctor"
        or result["role"] == "researcher"
        or result["role"] == "influencer"
    ):
        print(
            f"expertise dataaaaa-----{a.expertise.all()}---{type(a.expertise.all())}")
        result["expertise"] = get_expertise_data(a.expertise.all())
        result["doctor_other_details"] = serialize_model(
            a.doctordetails, DoctorDetailsSerializer
        )
    elif result["role"] == "patient":
        result["patient_other_details"] = serialize_model(
            a.patientdetails, PatientDetailsSerializer
        )
    else:
        pass
    return result


def serialize_model(a, serializer):

    result = {k: v for k, v in serializer(a).data.items()}
    print(f"in filter model data   {result}")
    return result


def GetDoctorOtherDetails(id, user_details):
    if (
        get_cu_user_type(id) == "influencer"
        or get_cu_user_type(id) == "doctor"
        or get_cu_user_type(id) == "researcher"
    ):
        if hasattr(user_details, "doctordetails"):
            print(
                f"user data detailssss--{user_details}----{user_details.doctordetails is not None}"
            )
            return serialize_model(user_details.doctordetails, DoctorDetailsSerializer)
        else:
            return "no doctor details found"


def get_expertise_data(expertise):
    p_data1 = []
    for x in expertise:
        p_data1.append(
            serialize_model(
                ExpertiseCancertype.objects.filter(id__exact=x.id)[0],
                ExpertiseCancertypeSerializer,
            )
        )

    return p_data1


class GetDoctorsByExpertise(generics.ListAPIView):
    permission_classes = []
    serializer_class = CuUserSerializer

    def get_queryset(self):
        if "expertise" in self.request.GET:
            if "," in self.request.query_params.get("expertise"):
                expertise_list = self.request.query_params.get(
                    "expertise").split(",")

            else:
                expertise_list = []
                expertise_list.append(
                    self.request.query_params.get("expertise"))
            exp_names = [
                x[0] for x in set(ExpertiseCancertype.objects.all().values_list("name"))
            ]
            expertise_objs = [
                ExpertiseCancertype.objects.get(name__exact=x)
                for x in expertise_list
                if x in exp_names
            ]
            xx = "cancer1" in exp_names
            print(
                f"expertise_listttt----{expertise_list}----{type(expertise_list)}---------{expertise_objs}------expppp{exp_names}---////{xx}"
            )
            experts_list_data = set(
                get_user_model().objects.filter(expertise__in=expertise_objs)
            )
            return experts_list_data

        elif "ask_ruchika" in self.request.GET:
            expertise_list = self.request.query_params.get("ask_ruchika")
            exp_list = expertise_list.split(" ")
            print(expertise_list)
            exp_l = ExpertiseCancertype.objects.filter(
                Q(name__in=exp_list) | Q(type__exact=expertise_list)
            )
            print(exp_l)
            expertise_objs = [
                ExpertiseCancertype.objects.get(id__exact=x.id) for x in exp_l
            ]
            print(expertise_objs)
            experts_list_data = set(
                get_user_model().objects.filter(expertise__in=expertise_objs)
            )
            print(experts_list_data)
            return experts_list_data
        else:
            expertise_list = ExpertiseCancertype.objects.all()
            expertise_list = expertise_list.values_list("name")
            expertise_objs = [
                ExpertiseCancertype.objects.get(name__icontains=x)
                for x in expertise_list
            ]
            experts_list_data = set(
                get_user_model().objects.filter(expertise__in=expertise_objs)
            )
            return experts_list_data

    def get(self, request, *args, **kwargs):

        res = self.list(request, *args, **kwargs)
        y = json.loads(json.dumps(res.data))

        data_list = []
        for x in y:
            print(f"expertssssssssssssssssssss{x['id']}--------------------")
            data = dict()
            a = get_user_model().objects.get(id=x["id"]).doctordetails
            f_data = filter_user_data(get_user_model().objects.get(id=x["id"]))
            #
            if f_data["doctor_other_details"]["ProfilePhoto"] is not None:
                # if f_data['doctor_other_details']['ProfilePhoto'].startswith("http"):
                #     is_valid_url = requests.get(f_data['doctor_other_details']['ProfilePhoto'])
                #     print(f"irrrrrrrrrrr prescription typeeee------------{is_valid_url.status_code}")
                #     if is_valid_url.status_code == 400 or is_valid_url.status_code == 403:
                #         obj_key = S3ObjectsKeys.objects.get(SUrl__exact=f_data['doctor_other_details']['ProfilePhoto'])
                #         new_obj_url = get_s3_signed_url_bykey(obj_key.Key)
                #         ddetails = DoctorDetails.objects.filter(DoctorId__exact=f_data['id'])[0]
                #         ddetails.ProfilePhoto = new_obj_url
                #         ddetails.save()
                #
                #         obj_key.SUrl = new_obj_url
                #         obj_key.save()
                #         f_data['doctor_other_details']['ProfilePhoto'] = ddetails.ProfilePhoto
                new_obj_url = get_s3_signed_url_bykey(
                    f_data["doctor_other_details"]["ProfilePhoto"]
                )
                f_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url
            #
            if f_data["doctor_other_details"]["IntVideoUrl"] is not None:
                # if f_data['doctor_other_details']['IntVideoUrl'].startswith('http'):
                #     is_valid_url = requests.get(f_data['doctor_other_details']['IntVideoUrl'])
                #     print(f"irrrrrrrrrrr prescription typeeee------------{is_valid_url.status_code}")
                #     if is_valid_url.status_code == 400 or is_valid_url.status_code == 403:
                #         obj_key = S3ObjectsKeys.objects.get(SUrl__exact=f_data['doctor_other_details']['IntVideoUrl'])
                #         new_obj_url = get_s3_signed_url_bykey(obj_key.Key)
                #         ddetails = DoctorDetails.objects.filter(DoctorId__exact=f_data['id'])[0]
                #         ddetails.IntVideoUrl = new_obj_url
                #         ddetails.save()
                #
                #         obj_key.SUrl = new_obj_url
                #         obj_key.save()
                #         f_data['doctor_other_details']['IntVideoUrl'] = ddetails.IntVideoUrl
                a_i = []
                b = f_data["doctor_other_details"]["IntVideoUrl"]
                for i in b:
                    new_obj_url = get_s3_signed_url_bykey(i)
                    a_i.append(new_obj_url)
                    print(f"ddd details video--------{new_obj_url}")
                f_data["doctor_other_details"]["IntVideoUrl"] = a_i

            # stories
            Apps = Appointments.objects.filter(slot_id__doctor__exact=x["id"])
            stories_count = 0
            rating = 0
            for z in Apps:
                stories = z.patientstories_set
                stories_count += stories.count()
                for v in stories.all():
                    rating += v.Rating

            rating_in_number = int(
                rating / stories_count) if stories_count != 0 else 0
            print(
                f"appssssssssssssssss{stories_count}------------{rating}-------{x['id']}----------{Apps}---"
            )
            # stories ends
            data.update(f_data)
            data.update({"patient_stories": stories_count,
                        "rating": rating_in_number})

            data_list.append(data)
            print(
                f"appssssssssssssssss1111111111111{data_list}---------------")

        return JsonResponse({"expert_data": data_list})


def SaveS3KeyObj(a):
    obj_key = a.split("/")[3]
    obj_key1 = obj_key.split("?")[0]
    print(f"signedkeyyyy{obj_key1}")
    res = S3ObjectsKeys.objects.create(Key=obj_key1, SUrl=a)

    return res


@method_decorator(csrf_exempt, name="dispatch")
class MedicalRecUpload(View):
    def post(self, request):
        print(settings.MEDIA_ROOT)
        print(request.FILES)
        form = MedicalRecordForm(request.POST, request.FILES)
        if form.is_valid():
            print("form data----", form.cleaned_data)
            user_type = get_cu_user_type(form.cleaned_data["patientid"])
            print("is patient----", user_type)
            if user_type != "patient":
                if user_type == "doctor":
                    return JsonResponse({"message": "user is not a patient"})
                return JsonResponse({"message": user_type})
            # #S3 upload
            # b1 = handle_uploaded_file(request.FILES['reportfile'])
            # f_name = b1.split('/')[2]
            # file_url1 = settings.PROJECT_ROOT + b1
            # file_url = handle_s3_uploaded_file(f_name, file_url1)
            # added for manyfile uploads in one go in patient
            p_recs = []
            i = 0
            while i < len(request.FILES):

                b1 = handle_uploaded_file(request.FILES[f"reportfile[{i}]"])
                print("medical records", b1)
                f_name = b1.split("/")[2]
                file_url = settings.PROJECT_ROOT + b1

                file_urls_res = handle_s3_uploaded_file(f_name, file_url)
                print(f"url from the medical records {file_urls_res}")
                p_recs.append(file_urls_res)
                i += 1

            # S3 upload ends
            rec = patient_medical_records.objects.create(
                reportname=form.cleaned_data["reportname"],
                reporttype=form.cleaned_data["reporttype"],
                generation_date=form.cleaned_data["generation_date"],
                reportsummary=form.cleaned_data["reportsummary"],
                report_file=p_recs,
                patient=get_cu_user(form.cleaned_data["patientid"]),
            )
            print("printing url----", file_url, rec, type(rec))
            return JsonResponse({"record_upload_status": "success"})
        else:
            print(f"form errors----{form.errors}")
            return JsonResponse(
                {"record_upload_status": "failed", "message": "invalid data"}
            )


class GetMedicalRecordsList(generics.ListAPIView):
    lookup_field = "email"
    serializer_class = patient_medical_recordsSerializer

    def get_queryset(self):
        email_val = self.kwargs.get(self.lookup_field)
        res = patient_medical_records.objects.filter(patient__email=email_val)
        if "start_date" in self.request.GET and self.request.GET["start_date"] != "":
            start_date = timezone.make_aware(
                parse_datetime(self.request.GET["start_date"]),
                timezone.get_current_timezone(),
            )
            res = res.filter(generation_date__gte=start_date)

        if "end_date" in self.request.GET and self.request.GET["end_date"] != "":
            end_date = timezone.make_aware(
                parse_datetime(self.request.GET["end_date"]),
                timezone.get_current_timezone(),
            )
            res = res.filter(generation_date__lte=end_date)

        if "type" in self.request.GET and self.request.GET["type"] != "":
            type_s = str(self.request.GET["type"]).lower()
            res = res.filter(report_file__icontains=self.request.GET["type"])

        if "search" in self.request.GET and self.request.GET["search"] != "":
            res = (
                res.filter(reportname__icontains=self.request.GET["search"])
                | res.filter(reportsummary__icontains=self.request.GET["search"])
                | res.filter(reporttype__icontains=self.request.GET["search"])
            )

        return res.order_by("-id")

    def get(self, request, *args, **kwargs):
        b = self.list(request, *args, **kwargs)
        # -----------------------------------------------------------------------
        total_items = len(b.data)
        # Get the page number from the request
        page_number = request.GET.get("page", 1)
        # Get the number of items per page from the request
        items_per_page = request.GET.get("per_page", 10)
        # for latest 4 medical records
        c = b.data[:5]
        latest_records = []
        for y in c:
            latest_records.append(y)
        paginator = Paginator(b.data, items_per_page)
        if int(page_number) not in range(1, int(paginator.num_pages) + 1):
            return HttpResponse("Not a valid page number", status=400)
        b.data = paginator.page(page_number)
        med_rec_data = []
        for y in b.data:
            if y["report_file"] is not None:
                r_file = y["report_file"]
                r_f = []
                for i in r_file:
                    new_obj_url = get_s3_signed_url_bykey(i)
                    print(new_obj_url)
                    r_f.append(new_obj_url)
                y["report_file"] = r_f
                med_rec_data.append(y)
        response_data = {
            "total_items": total_items,
            "total_pages": paginator.num_pages,
            "items": med_rec_data,
            "latest_records": latest_records,
        }

        return JsonResponse(response_data)


class PatientQuery(generics.CreateAPIView):
    serializer_class = PatientQueriesSerializer

    def post(self, request, *args, **kwargs):
        try:
            appt_id = request.data.get('ApptId')
            if not appt_id:
                raise ValueError("Appointment ID is required")

            appointment = (
                Appointments.objects.select_related(
                    "slot_id__doctor", "patient")
                .filter(id=appt_id)
                .first()
            )

            if not appointment:
                return Response({"error": "Appointment not found."}, status=404)

            slot = appointment.slot_id
            doctor = slot.doctor
            patient = appointment.patient

            payment = PatientPayment.objects.filter(
                Q(AppointmentId=appointment)
            ).first()
            
            amount = payment.amount if payment else None

            if not doctor or not patient:
                return Response({"error": "Doctor or patient information missing."}, status=400)

            resp = self.create(request, *args, **kwargs)

            app_url_doctor = os.getenv(
                "NEXT_CANCER_UNWIRED_DOCTOR_APP", "") + "/inbox"

            app_url_patient = os.getenv(
                "NEXT_CANCER_UNWIRED_PATIENT_APP", "") + "/myprofile?tab=inbox"

            if email_check("Query asked!"):
                email_service.reply_patient_query_email(
                    doctor.email, doctor.name, app_url_doctor, patient.name,amount)
                # SendEmailReplyQuery(reply_to_id, app_url, user.id, user.name)

            if notification_check("Query asked!"):
                send_generic_push_notification(
                    patient.id, "Query asked!", f"Hi {patient.name}, Your query is submitted for the recent appointment with {doctor.name}", app_url_patient)
                send_generic_push_notification(
                    doctor.id, "Query asked!", f"Hi {doctor.name}, New query is submitted by {patient.name} for recent appointment", app_url_doctor)

            return resp

        except Exception as e:
            print(f"PatientQuery API error: {e}")
            raise e


class PatientStory(generics.ListCreateAPIView):
    permission_classes = []
    serializer_class = PatientStoriesSerializer

    def get_queryset(self):

        if get_cu_user_type(self.kwargs["user_id"]) in [
            "doctor",
            "researcher",
            "influencer",
        ]:

            if self.kwargs["status"] in ["1", "0", "2"]:
                data = PatientStories.objects.filter(
                    ApptId__slot_id__doctor__exact=self.kwargs["user_id"],
                    status__exact=int(self.kwargs["status"]),
                )
            else:
                data = PatientStories.objects.filter(
                    ApptId__slot_id__doctor__exact=self.kwargs["user_id"],
                    status__in=["0", "1", "2"],
                )

        elif get_cu_user_type(self.kwargs["user_id"]) == "patient":
            if self.kwargs["status"] in ["1", "0" "2"]:
                data = PatientStories.objects.filter(
                    ApptId__patient__exact=self.kwargs["user_id"],
                    status__exact=int(self.kwargs["status"]),
                )
            else:
                data = PatientStories.objects.filter(
                    ApptId__patient__exact=self.kwargs["user_id"],
                    status__in=["0", "1", "2"],
                )
        # -------------added to get all the patient stories for all the users for approval-------------------------------------------------
        elif self.kwargs["user_id"] == "all":
            data = PatientStories.objects.filter(status__exact=2)
        # ---------------------------------------------------------------------------------------------------------------------------------
        else:
            data = []

        return data

    def get(self, request, *args, **kwargs):

        res = self.list(request, *args, **kwargs)
        y = json.loads(json.dumps(res.data))
        # -------------added to get all the patient stories for all the users for approval-------------------------------------------------
        if self.kwargs["user_id"] == "all":
            # Get the page number from the request
            page_number = request.GET.get("page", 1)
            # Get the number of items per page from the request
            items_per_page = request.GET.get("per_page", 10)
            total_items = len(y)
            paginator = Paginator(y, items_per_page)
            if int(page_number) not in range(1, int(paginator.num_pages) + 1):
                return HttpResponse("Not a valid page number", status=400)
            y = paginator.page(page_number)
            pat_s = []
            for x in y:
                print("Appointment idssssssssssssssssssssssss", x["ApptId"])
                user_obj = Appointments.objects.get(id__exact=x["ApptId"])
                patient_name = user_obj.patient.name
                patient_photo = user_obj.patient.patientdetails.ProfilePhoto
                doctor_name = user_obj.slot_id.doctor.name
                doctor_id = user_obj.slot_id.doctor.id
                doctor_photo = user_obj.slot_id.doctor.doctordetails.ProfilePhoto
                x["patient_name"] = patient_name
                if patient_photo is not None:
                    x["patient_photo"] = get_s3_signed_url_bykey(patient_photo)
                else:
                    x["patient_photo"] = patient_photo

                # ----------adding experts details
                x["expert_id"] = doctor_id
                x["expert_name"] = doctor_name
                if doctor_photo is not None:
                    x["expert_photo"] = get_s3_signed_url_bykey(doctor_photo)
                else:
                    x["expert_photo"] = doctor_photo
                x["expert_role"] = get_cu_user_type(doctor_id)
                pat_s.append(x)
            return JsonResponse(
                {
                    "total_items": total_items,
                    "total_pages": paginator.num_pages,
                    "items": pat_s,
                }
            )
        # ---------------------------------------------------------------------------------------------------------------------------------
        else:
            for x in y:
                patient_name = Appointments.objects.get(
                    id__exact=x["ApptId"]
                ).patient.name
                patient_photo = Appointments.objects.get(
                    id__exact=x["ApptId"]
                ).patient.patientdetails.ProfilePhoto
                x["patient_name"] = patient_name
                if patient_photo is not None:
                    x["patient_photo"] = get_s3_signed_url_bykey(patient_photo)
                else:
                    x["patient_photo"] = patient_photo

        return Response(y)

    def post(self, request, *args, **kwargs):
        print(f"patient stories reqqqq------{request.data['ApptId']}")

        if PatientStories.objects.filter(ApptId=request.data["ApptId"]).exists():
            return JsonResponse(
                {"message": "Feedback has already been submitted for this appointment."},
                status=400
            )

        resp = self.create(request, *args, **kwargs)
        print(f"patient stories------{resp}")

        if isinstance(resp, Response) and resp.status_code == 201:
            # Check if notification is enabled
            noti_check = notification_check("Testimonial approval!")
            if noti_check:
                try:
                    appt = Appointments.objects.get(
                        id__exact=request.data["ApptId"])
                    expert_id = appt.slot_id.doctor.id  # Fetching expert ID from the appointment
                    NotifyAdminForApproval(
                        "Testimonial submission!", expert_id, "patient-stories")
                    print(
                        f"Notification sent for patient story submission by {expert_id}")
                except Exception as e:
                    logger.error(f"Error sending notification: {str(e)}")

        return resp


# added


class UpdatePatientStoryView(generics.UpdateAPIView):
    serializer_class = PatientStoriesSerializer
    queryset = PatientStories.objects.all()
    lookup_field = "id"

    def put(self, r, *args, **kwargs):
        a = self.partial_update(r, *args, **kwargs)
        # ----------push notification-------------------------
        if "status" in r.data:
            expert_id = PatientStories.objects.get(id__exact=self.kwargs["id"])
            noti_check = notification_check("Testimonial approval!")
            if noti_check == True:
                r_p = AdminApprovePushNotification(
                    "Testimonial approval!",
                    expert_id.ApptId.slot_id.doctor.id,
                    r.data["status"],
                    r.GET["user_id"],
                    "N",
                )
            else:
                pass
        else:
            pass
        # ------------------------------------------------------------------
        return a

    def delete(self, r, *args, **kwargs):
        p_id = PatientStories.objects.filter(id__exact=self.kwargs["id"])
        if p_id.exists():
            p_id = PatientStories.objects.get(id__exact=self.kwargs["id"])
            a = ContentRemoval(p_id, "patient_stories")
            return Response(a)
        else:
            return Response("Item doesn't exist", status=404)


@method_decorator(csrf_exempt, name="dispatch")
class CancerTypesMainView(View):
    def get(self, r):
        api_url = "https://oncotree.info/api/mainTypes"
        # headers = {"Content-Type": "application/json"}

        try:
            response = requests.get(api_url)
            a = response.json()
            print(f"cancer types response----{a}")

            return JsonResponse(a, safe=False)
        except Exception as e:
            print(f"get user exception----{e}")
            return JsonResponse(e, safe=False)


@method_decorator(csrf_exempt, name="dispatch")
class CancerTypesCatView(View):
    def post(self, r):
        y = json.loads(r.body)
        api_url = (
            "https://oncotree.info/api/tumorTypes/search/mainType/" +
            y["category"]
        )
        print(f"apiyurllll----{api_url}")

        # headers = {"Content-Type": "application/json"}

        try:
            response = requests.get(api_url)
            a = response.json()
            print(f"cancer types response category----{a}")

            return JsonResponse(a, safe=False)
        except Exception as e:
            print(f"get user exception----{e}")
            return JsonResponse(e, safe=False)


@method_decorator(csrf_exempt, name="dispatch")
class CancerTypesLevelView(View):
    def post(self, r, tumour_level):
        y = json.loads(r.body)
        exactMatch = "true" if y["exactMatch"] == 1 else "false"
        l1 = list(y["levels"])
        aa = ",".join([str(elem) for elem in l1])

        api_url = (
            "https://oncotree.info/api/tumorTypes/search/level/"
            + str(tumour_level)
            + "?exactMatch="
            + exactMatch
            + "&levels="
            + aa
        )

        try:
            response = requests.get(api_url)
            a = response.json()
            b = json.loads(json.dumps(a))

            return JsonResponse(a, safe=False)
        except Exception as e:
            print(f"get user exception----{e}")
            return JsonResponse(e, safe=False)


class GetDoctorDetailsView(generics.RetrieveAPIView):
    permission_classes = []
    serializer_class = CuUserRegisterSerializer
    queryset = CuUser.objects.all()
    lookup_field = "id"

    def get(self, r, *args, **kwargs):
        res = self.retrieve(r, *args, **kwargs)
        # y = json.loads(json.dumps(res.data))
        #
        user_details = get_cu_user(res.data["id"])
        user_data = filter_user_data(user_details)
        if user_data["doctor_other_details"]["ProfilePhoto"] is not None:
            new_obj_url = get_s3_signed_url_bykey(
                user_data["doctor_other_details"]["ProfilePhoto"]
            )
            user_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url
        if user_data["doctor_other_details"]["IntVideoUrl"] is not None:
            a_i = []
            b = user_data["doctor_other_details"]["IntVideoUrl"]
            for i in b:
                new_obj_url = get_s3_signed_url_bykey(i)
                a_i.append(new_obj_url)
                print(f"ddd details video--------{new_obj_url}")
            user_data["doctor_other_details"]["IntVideoUrl"] = a_i
        exp_rank = ExpertRank.objects.filter(
            ExpertId_id__exact=self.kwargs["id"])
        if exp_rank.exists():
            user_data["expert_rank"] = exp_rank[0].rank
        else:
            user_data["expert_rank"] = None
        Apps = Appointments.objects.filter(
            slot_id__doctor__exact=self.kwargs["id"])
        doctors_s = CuUser.objects.filter(id__exact=self.kwargs["id"])
        stories_count = 0
        reviews_count = 0
        rating_s = 0
        rating_r = 0
        for z in Apps:
            stories = z.patientstories_set
            stories_count += stories.count()
            for v in stories.all():
                rating_s += v.Rating
        for z in doctors_s:
            reviews = z.doctorreviews_set
            for u in reviews.all():
                if u.ReviewStatus == 2:
                    rating_r += u.ReviewRating
                    reviews_count += 1
        rating_in_number1 = int(
            rating_s / stories_count) if stories_count != 0 else 0
        rating_in_number2 = int(
            rating_r / reviews_count) if reviews_count != 0 else 0
        rating_in_number = int((rating_in_number1 + rating_in_number2) / 2)
        # print(f"appssssssssssssssss{stories_count}------------{rating}-------{x.id}----------{Apps}---")
        user_data["patient_stories"] = stories_count
        user_data["doctor_reviews"] = reviews_count
        user_data["rating"] = rating_in_number
        # return JsonResponse({"data":filter_user_data(get_user_model().objects.get(id__exact=y['id']))})
        return JsonResponse({"data": user_data})


def refreshS3URL(photo, id):
    print(f"stttttttttttt----------------{photo.startswith('http')}")
    if photo.startswith("http"):
        is_valid_url = requests.get(photo)
        print(f"typeeee------{is_valid_url.status_code}")
        if is_valid_url.status_code == 400 or is_valid_url.status_code == 403:
            obj_key = S3ObjectsKeys.objects.get(SUrl__exact=photo)
            new_obj_url = get_s3_signed_url_bykey(obj_key.Key)
            pdetails = PatientDetails.objects.get(PatientId__exact=id)
            pdetails.ProfilePhoto = new_obj_url
            pdetails.save()

            obj_key.SUrl = new_obj_url
            obj_key.save()
            return pdetails.ProfilePhoto


class GetTestimonialsView(generics.ListAPIView):
    permission_classes = []
    serializer_class = PatientStoriesSerializer
    queryset = PatientStories.objects.all()
    # lookup_field = 'id'

    def get_queryset(self):
        if self.kwargs["id"] != "all":

            return PatientStories.objects.filter(
                status__exact=1, ApptId__slot_id__doctor__exact=self.kwargs["id"]
            )
        elif self.kwargs["id"] == "all":
            if "name" in self.request.GET:
                return PatientStories.objects.filter(
                    status__exact=1,
                    ApptId__patient__name__icontains=self.request.GET["name"],
                )
            else:
                return PatientStories.objects.filter(status__exact=1)
        else:

            return []

    def get(self, r, *args, **kwargs):
        res = self.list(r, *args, **kwargs)

        b = json.loads(json.dumps(res.data))
        page_number = r.GET.get("page", 1)
        # Get the number of items per page from the request
        items_per_page = r.GET.get("per_page", 10)
        total_items = len(b)
        paginator = Paginator(b, items_per_page)
        if int(page_number) not in range(1, int(paginator.num_pages) + 1):
            return HttpResponse("Not a valid page number", status=400)
        b = paginator.page(page_number)
        app_items = []
        for x in b:

            p1 = Appointments.objects.get(id__exact=x["ApptId"]).patient
            aa = ContentSelection.objects.filter(
                c_id__exact=x["id"], category__exact="Testimonials"
            )
            if aa.exists():
                x["selected_content_rank"] = aa[0].rank
                x["selected_content_id"] = aa[0].id
            else:
                x["selected_content_rank"] = None
                x["selected_content_id"] = None
            print(f"dict------{x}-----{type(x)}")
            x["patient_name"] = p1.name
            new_obj_url = get_s3_signed_url_bykey(
                p1.patientdetails.ProfilePhoto)
            x["patient_photo"] = new_obj_url
            app_items.append(x)
        response_data = {
            "total_items": total_items,
            "total_pages": paginator.num_pages,
            "items": app_items,
        }
        return JsonResponse(response_data, safe=False)


class GetDoctorFeedbacks(generics.ListAPIView):
    permission_classes = []
    serializer_class = ExpertFeedbackSerializer
    queryset = ExpertFeedback.objects.all()
    # lookup_field = 'id'

    def get_queryset(self):
        if "name" in self.request.GET:
            return ExpertFeedback.objects.filter(
                status__exact=1, ExpertId__name__icontains=self.request.GET["name"]
            )
        else:
            return ExpertFeedback.objects.filter(status__exact=1)

    def get(self, r, *args, **kwargs):
        res = self.list(r, *args, **kwargs)
        b = json.loads(json.dumps(res.data))
        page_number = r.GET.get("page", 1)
        # Get the number of items per page from the request
        items_per_page = r.GET.get("per_page", 10)
        total_items = len(b)
        paginator = Paginator(b, items_per_page)
        if int(page_number) not in range(1, int(paginator.num_pages) + 1):
            return HttpResponse("Not a valid page number", status=400)
        b = paginator.page(page_number)
        app_items = []
        for x in b:
            aa = ContentSelection.objects.filter(
                c_id__exact=x["id"], category__exact="ExpertReviews"
            )
            if aa.exists():
                x["selected_content_rank"] = aa[0].rank
                x["selected_content_id"] = aa[0].id
            else:
                x["selected_content_rank"] = None
                x["selected_content_id"] = None
            e1 = get_user_model().objects.get(id__exact=x["ExpertId"])
            print(f"dict------{x}-----{type(x)}")
            x["expert_name"] = e1.name
            x["prefix"] = e1.prefix
            x["expert_role"] = get_cu_user_type(x["ExpertId"])
            new_obj_url = get_s3_signed_url_bykey(
                e1.doctordetails.ProfilePhoto)
            x["expert_photo"] = new_obj_url
            app_items.append(x)
        response_data = {
            "total_items": total_items,
            "total_pages": paginator.num_pages,
            "items": app_items,
        }
        return Response(response_data)


# class GetFeesView(views.APIView):
#     def post(self, r):
#         doctor_fees = DoctorDetails.objects.get(
#             DoctorId__exact=int(r.data["user_id"])
#         ).ConsultationFees
#         # added dynamic fees calculation
#         f = SchedulerSlots.objects.get(
#             doctor_id__exact=r.data["user_id"], id__exact=r.data["slot_id"]
#         )
#         f = f.schedule_end_time - f.schedule_start_time
#         f = f.total_seconds() / 1800
#         print(f"-----------time---------{f}------------")
#         base_charges = f * doctor_fees + Pricing.objects.last().PlatformCharges
#         transaction_charges = (
#             base_charges * Pricing.objects.last().TransactionCharges / 100
#         )
#         print(
#             f"final fees------{doctor_fees}-------{base_charges}------{transaction_charges}"
#         )
#         return JsonResponse(
#             {
#                 "doctor_fees": doctor_fees,
#                 "platform_charges": Pricing.objects.last().PlatformCharges,
#                 "transaction_charges": transaction_charges,
#                 "final_charge": base_charges + transaction_charges,
#             }
#         )

class GetFeesView(views.APIView):
    def post(self, request):
        doctor_id = int(request.data["user_id"])
        slot_id = request.data["slot_id"]

        try:
            fee_data = calculate_fees(doctor_id, slot_id)

            return JsonResponse(fee_data)

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)

# def CreateProduct(p_desc):
#     stripe.api_key = os.getenv("STRIPE_API_KEY")

#     prod_name = stripe.Product.create(
#             name=p_desc,
#             description=p_desc+"_desc",
#         )
#     print(f"product id: {prod_name.id}--------------{prod_name}----{type(prod_name)}---{prod_name is not None}")
#     return prod_name

# def CreatePrice(id,amt,curr):
#     stripe.api_key = os.getenv("STRIPE_API_KEY")
#     prod_price = stripe.Price.create(
#         unit_amount=amt,
#         currency=curr,
#         product=id,
#     )
#     print(f"price id: {prod_price.id}----------{prod_price}")
#     return prod_price


# class CreatePLinkView(views.APIView):

#     def post(self,r):
#         redirect_url = "https://healthunwired.com" + "/paymentsuccess?sid={{CHECKOUT_SESSION_ID}}"
#         cancel_url = "https://healthunwired.com" + "/paymentfailed?sid={{CHECKOUT_SESSION_ID}}"


#         stripe.api_key = os.getenv("STRIPE_API_KEY")
#         prd=CreateProduct(str(r.data['user_id'])+"_"+r.data['title'])

#         priceid=CreatePrice(prd.id,r.data['amt'],r.data['curr'])

#         print(f"iddddddddddd{prd.id}------------------{priceid}")
#         try:
#             #a=stripe.PaymentLink.create(line_items=[{"price": priceid, "quantity": 1}],metadata={"user_id":r.data['user_id'],'slot_id':r.data['slot_id'],'summary':r.data['title'],'desc':r.data['desc'],'location':r.data['location']},restrictions={'completed_sessions':{'limit':5}},shipping_address_collection={'allowed_countries':['IN','US']},invoice_creation={'enabled':True},after_completion={"type": "redirect", "redirect": {"url": redirect_url}})
#             a=stripe.checkout.Session.create(
#                 #customer=r.data['user_id'],
#                 customer_email=r.data['user_email'],
#                 line_items=[{"price": priceid, "quantity": 1}],
#                 metadata={"user_id": r.data['user_id'], 'slot_id': r.data['slot_id'], 'summary': r.data['title'],
#                           'desc': r.data['desc'], 'location': r.data['location']},
#                 shipping_address_collection={'allowed_countries': ['IN', 'US']},
#                 success_url="https://healthunwired.com" + "/paymentsuccess?sid={{CHECKOUT_SESSION_ID}}",
#                 cancel_url="https://healthunwired.com" + "/paymentsuccess?sid={{CHECKOUT_SESSION_ID}}",
#                 invoice_creation={'enabled': True},
#                 mode="payment",
#             )
#             print(f"payment id:--------------{a}----{type(a)}---{a is not None}-----")
#         except Exception as e:
#             print(f"event exceptionnnnn:--------------{e}----")

#         if a is not None:
#             print(f'-------------------{a}------------------')
#             return JsonResponse({"payment_link": a.url})
#         else:
#             return JsonResponse({"message": "paymt link not created"})
#         #return redirect(a.url, code=303)

#     def get(self,r):
#         print(f"sesssssssssssssssssssss{r.GET['sess_id']}")
#         stripe.api_key = os.getenv("STRIPE_API_KEY")
#         a=stripe.checkout.Session.retrieve(
#             r.GET['sess_id'],
#         )
#         print(f"session details:--------------{a}----{type(a)}---{a is not None}")
#         if a is not None:
#             return JsonResponse({"message": a})
#         else:
#             return JsonResponse({"message": "no session details"})


AIRWALLEX_API_BASE = "https://api.airwallex.com/v1"
AIRWALLEX_API_KEY = os.getenv("AIRWALLEX_API_KEY")


def create_airwallex_payment_intent(amount, currency, metadata, customer_email, title, redirect_url, cancel_url):
    print(f"amount={amount},metadata={metadata} ")
    payload = "{}"
    headers = {
        "Content-Type": "application/json",
        "x-api-key": os.getenv("AIRWALLEX_API_KEY"),
        "x-client-id": os.getenv("AIRWALLEX_CLIENT_ID"),
    }

    url1 = f"https://api-demo.airwallex.com/api/v1/authentication/login"

    res = requests.post(url1, json=payload, headers=headers)
    data = res.json()

    print(data)
    url = f"https://api-demo.airwallex.com/api/v1/pa/payment_links/create"
    headers = {
        "Authorization": f"Bearer {data['token']}",
        "Content-Type": "application/json",
    }
    payload = {
        "request_id": str(uuid.uuid4()),
        "amount": amount,
        "currency": currency,
        "customer": {
            "email": customer_email,
        },
        "reusable": False,
        "title": title,
        "metadata": metadata,
        "return_url": redirect_url,
        "cancel_url": cancel_url,
    }

    response = requests.post(url, json=payload, headers=headers)
    response_data = response.json()

    # if response.status_code != 200:
    #     raise Exception(f"Error creating Payment Intent: {response_data}")

    return response_data


def create_beneficiary(amount, currency, metadata, customer_email, title, redirect_url, cancel_url):

    payload = "{}"

    headers = {
        "Content-Type": "application/json",
        "x-api-key": os.getenv("AIRWALLEX_API_KEY"),
        "x-client-id": os.getenv("AIRWALLEX_CLIENT_ID"),
    }

    url1 = f"https://api-demo.airwallex.com/api/v1/authentication/login"

    res = requests.post(url1, json=payload, headers=headers)
    data = res.json()

    print(data)
    url = f"https://api-demo.airwallex.com/api/v1/beneficiaries/create"
    headers = {
        "Authorization": f"Bearer {data['token']}",
        "Content-Type": "application/json",
    }
    payload = {
        "request_id": str(uuid.uuid4()),
        "amount": amount,
        "currency": currency,
        "customer": {
            "email": customer_email,
        },
        "reusable": True,
        "title": title,
        "metadata": metadata,
        "return_url": redirect_url,
        "cancel_url": cancel_url,
    }

    response = requests.post(url, json=payload, headers=headers)
    response_data = response.json()

    # if response.status_code != 200:
    #     raise Exception(f"Error creating Payment Intent: {response_data}")

    return response_data

# testing create beneficiary
# class GetAllExpertise(generics)


class TestJson(generics.CreateAPIView):

    def post(self, request, *args, **kwargs):
        try:
            # Create Payment Intent in Airwallex
            # ben = create_beneficiary(amount, currency, metadata, customer_email, title, redirect_url, cancel_url):

            # # Extract the Payment Link URL
            # print(ben)
            payment_url = 'url'
            if payment_url:
                return JsonResponse({"payment_link": payment_url})
            else:
                return JsonResponse({"message": "Payment link not created"}, status=400)

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)


class CreatePLinkView(views.APIView):
    def post(self, request):
        # Define redirect and cancel URLs
        redirect_url = "https://healthunwired.com/paymentsuccess"
        cancel_url = "https://healthunwired.com/paymentfailed"

        # Collect data from the request
        user_id = request.data.get('user_id')
        title = request.data.get('title')
        amount = request.data.get('amt')
        currency = request.data.get('curr', 'USD')
        customer_email = request.data.get('user_email')

        metadata = {
            "user_id": str(user_id),  # Ensure string conversion
            # Ensure string conversion
            "slot_id": str(request.data.get('slot_id')),
            "summary": title,
            "title": title,
            "desc": request.data.get('desc'),
            "location": request.data.get('location'),
        }

        for key, value in metadata.items():
            if value is None:
                metadata[key] = ""

        try:
            # Create Payment Intent in Airwallex
            payment_intent = create_airwallex_payment_intent(
                amount=amount,
                currency=currency,
                metadata=metadata,
                customer_email=customer_email,
                redirect_url=redirect_url,
                cancel_url=cancel_url,
                title=title,
            )

            # Extract the Payment Link URL
            print(
                "this is the response of the creation of the payment link ", payment_intent)
            payment_url = payment_intent['url']
            if payment_url:
                AppointmentId = CreateOrder(user_id, request.data.get(
                    'slot_id'), title, request.data.get('location'), request.data.get('desc'))
                FulfillOrder(AppointmentId, user_id,
                             request.data.get('slot_id'))
                return JsonResponse({"payment_link": payment_url})
            else:
                return JsonResponse({"message": "Payment link not created"}, status=400)

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)

    def get(self, request):
        # Retrieve Payment Intent details by ID
        payment_intent_id = request.GET.get("sess_id")
        url = f"{AIRWALLEX_API_BASE}/payment_intents/{payment_intent_id}"
        headers = {
            "Authorization": f"Bearer {AIRWALLEX_API_KEY}",
            "Content-Type": "application/json",
        }

        try:
            response = requests.get(url, headers=headers)
            response_data = response.json()

            if response.status_code == 200:
                return JsonResponse({"message": response_data})
            else:
                return JsonResponse({"message": "No session details found"}, status=400)

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)


class PaymentProcessingView(TemplateView):
    permission_classes = []
    """
    Bridge page that handles redirecting from Airwallex to the success page
    """
    template_name = 'payment_processing.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # You can add context data here if needed
        context['logo_url'] = True  # Set to True if your template uses a logo
        return context


# views.py
# views.py
# def _map_status(airwallex_status):
#     """Convert Airwallex status to your system's status"""
#     status_map = {
#         'requires_payment_method': 'REQUIRES_PAYMENT_METHOD',
#         'requires_confirmation': 'REQUIRES_CONFIRMATION',
#         'requires_action': 'REQUIRES_ACTION',
#         'processing': 'PROCESSING',
#         'succeeded': 'SUCCEEDED',
#         'failed': 'FAILED',
#         'canceled': 'CANCELED'
#     }
#     return status_map.get(airwallex_status.lower(), 'PROCESSING')


# class PaymentStatusView(APIView):
#     def get(self, request, payment_intent_id):
#         # Validate payment_intent_id
#         if not payment_intent_id or not isinstance(payment_intent_id, str):
#             return Response(
#                 {"error": "Payment intent ID is required and must be a string."},
#                 status=status.HTTP_400_BAD_REQUEST
#             )

#         if not payment_intent_id.startswith("int_"):
#             return Response(
#                 {"error": "Invalid payment_intent_id format. Must start with 'int_'."},
#                 status=status.HTTP_400_BAD_REQUEST
#             )

#         try:
#             # First check if local payment exists
#             local_payment = PatientPayment.objects.filter(
#                 PaymentIntent=payment_intent_id
#             ).first()

#             if not local_payment:
#                 # If no local record exists, check with Airwallex first
#                 airwallex = AirwallexService()
#                 payment = airwallex.get_payment_intent(payment_intent_id)

#                 if not payment:
#                     return Response(
#                         {
#                             "error": "Payment intent not found in either local system or payment provider.",
#                             "payment_intent_id": payment_intent_id
#                         },
#                         status=status.HTTP_404_NOT_FOUND
#                     )

#                 # If found in Airwallex but not locally, create a record
#                 local_payment = PatientPayment.objects.create(
#                     PaymentIntent=payment_intent_id,
#                     raw_status=payment['status'],
#                     payment_status=_map_status(payment['status']),
#                     # Add other required fields here
#                     amount=payment.get('amount'),
#                     currency=payment.get('currency')
#                 )
#                 return Response(
#                     {
#                         "message": "Payment record created from provider data.",
#                         "airwallex_status": payment['status'],
#                         "your_system_status": local_payment.payment_status
#                     },
#                     status=status.HTTP_201_CREATED
#                 )

#             # Normal flow when local payment exists
#             airwallex = AirwallexService()
#             payment = airwallex.get_payment_intent(payment_intent_id)

#             if not payment or 'status' not in payment:
#                 return Response(
#                     {"error": "Invalid response from payment provider."},
#                     status=status.HTTP_502_BAD_GATEWAY
#                 )

#             if local_payment.raw_status != payment['status']:
#                 local_payment.raw_status = payment['status']
#                 local_payment.payment_status = _map_status(payment['status'])
#                 local_payment.save()

#             return Response({
#                 "payment_intent_id": payment_intent_id,
#                 "airwallex_status": payment['status'],
#                 "your_system_status": local_payment.payment_status
#             })

#         except Exception as e:
#             logger.error(
#                 f"Error processing payment status for {payment_intent_id}: {str(e)}")
#             return Response(
#                 {
#                     "error": "Unable to process payment status request.",
#                     "details": str(e)
#                 },
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


class PaymentLinkView(views.APIView):
    def post(self, request):
        redirect_url = "https://healthunwired.com/paymentsuccess"
        cancel_url = "https://healthunwired.com/paymentfailed"

        try:
            slot_id = request.data.get('slot_id')
            user_id = request.data['user_id']

            # Check if payment already exists and is completed
            existing_payment = PatientPayment.objects.filter(
                Q(PatientId=user_id),
                Q(AppointmentId__slot_id=slot_id),
                Q(raw_status='PAID') | Q(payment_status='SUCCEEDED') | Q(
                    payment_status='PROCESSING')
            ).select_related('AppointmentId').first()

            if existing_payment:
                return Response({
                    "payment_url": f"/myprofile?tab=appointments",
                    "already_paid": True,
                    "appointment_id": existing_payment.AppointmentId.id
                }, status=200)

            # Check if slot is already booked
            if Appointments.objects.filter(slot_id=slot_id, status__in=['P', 'B']).exists():
                return Response(
                    {"error": "Slot is already booked"},
                    status=400
                )

            # No existing payment - create new payment link
            airwallex = AirwallexService()
            payment_link = airwallex.create_payment_link(
                amount=request.data['amt'],
                currency=request.data.get('curr', 'USD'),
                metadata={
                    "user_id": user_id,
                    "slot_id": slot_id,
                    "title": request.data['title'],
                    "desc": request.data.get('desc', ''),
                    "location": request.data.get('location', ''),
                    "redirect_url": redirect_url,
                    "cancel_url": cancel_url
                },
                customer_email=request.data['user_email'],
                title=request.data['title'],
                redirect_url=redirect_url,
                cancel_url=cancel_url
            )

            return Response({
                "payment_url": payment_link['url'],
                "payment_id": payment_link['id'],
                "already_paid": False
            })

        except Exception as e:
            return Response({"error": str(e)}, status=400)


class WebhookView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        redirect_url = "https://healthunwired.com/paymentsuccess"
        try:
            # Read and verify request
            raw_body = request.body
            event = json.loads(raw_body)
            event_name = event.get('name')

            print(f"Processing {event_name} event")

            # Payment Success Events
            if event_name in ['payment_intent.succeeded', 'payment_link.paid']:
                return self._handle_payment_success(event, redirect_url)

            # Refund Events
            elif event_name == 'refund.succeeded':
                return self._handle_refund(event)

            # Failure Events
            elif event_name == 'payment_intent.failed':
                return self._handle_payment_failed(event)

            else:
                print(f"Ignoring unhandled event: {event_name}")
                return Response({"status": "ignored"}, status=200)

        except Exception as e:
            print(f"Webhook processing failed: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def _handle_payment_success(self, event, redirect_url):
        try:
            payment_object = event['data']['object']
            raw_status = payment_object['status']

            # Extract metadata from multiple possible locations
            metadata = (
                payment_object.get('metadata') or
                payment_object.get('latest_payment_attempt', {}).get('metadata') or
                {}
            )

            # Validate required metadata
            if not all(key in metadata for key in ['user_id', 'slot_id']):
                print(
                    f"Missing required metadata in payment {payment_object['id']}")
                return Response(
                    {"error": "Missing user_id or slot_id in metadata"},
                    status=400
                )

             # Check slot availability before creating appointment
            slot_id = metadata['slot_id']
            if Appointments.objects.filter(slot_id=slot_id, status__in=['P', 'B']).exists():
                return Response(
                    {"error": f"Slot {slot_id} is already booked or pending"},
                    status=400
                )

            # Get payment details (convert amount from cents to dollars if needed)
            amount = payment_object.get('amount')
            currency = payment_object.get('currency', 'USD')
            # amount_decimal = Decimal(amount) / 100 if amount else None

            # Create appointment and payment record
            appointment = CreateOrder(
                payment_intent_id=payment_object['latest_successful_payment_intent_id'],
                # payment_intent_id=payment_object['id'],
                user_id=metadata['user_id'],
                slot_id=metadata['slot_id'],
                summary=metadata.get('title', ''),
                location=metadata.get('location', ''),
                desc=metadata.get('desc', ''),
                currency=currency,
                amount=amount,
                raw_status=raw_status
            )

            if not appointment:
                return Response({"error": "Appointment creation failed"}, status=400)

            # Fulfill the appointment
            fulfillment_result = FulfillOrder(
                appointment_id=appointment,
                user_id=metadata['user_id'],
                slot_id=metadata['slot_id']
            )

            if not fulfillment_result:
                return Response({"error": "Fulfillment failed"}, status=400)
            print(
                f"Payment success processed for payment, redirecting to {redirect_url}")

            return Response({
                "status": "success",
                "redirect_url": redirect_url,
                "appointment_id": appointment.id
            }, status=200)

        except Exception as e:
            print(f"Payment processing failed: {str(e)}")
            return Response({
                "error": str(e),
                "redirect_url": "https://healthunwired.com/paymentfailed"
            }, status=500)

    def _handle_payment_attempt(self, event):
        payment_intent_id = event['data']['object']['payment_intent_id']
        payment_intent = self._get_payment_intent(payment_intent_id)

        if payment_intent and payment_intent.get('status') == 'SUCCEEDED':
            return self._handle_payment_success({
                'name': 'payment_intent.succeeded',
                'data': {'object': payment_intent}
            })
        return Response({"status": "processing"}, status=200)

    def _handle_payment_failed(self, event):
        print(f"Payment failed: {event['data']['object']['id']}")
        # Add your failure handling logic here
        return Response({"status": "failed_handled"}, status=200)

    # --- NEED TO CHECK IS IN USE OR NOT ---
    def _handle_refund(self, event):
        refund_data = event['data']['object']
        payment_intent_id = refund_data['payment_intent_id']

        payment = PatientPayment.objects.get(PaymentIntent=payment_intent_id)
        appointment = payment.AppointmentId
        patient = appointment.patient
        doctor = appointment.slot_id.doctor
        
        # Get appointment timings
        slot_details = get_appointment_details(
            slot_id=appointment.slot_id_id,
            doctor_id=doctor.id
        )
        appointment_timings = slot_details["appointment_timings"]
        refund_amount = refund_data['amount']
        try:
            self._update_refund_records(
                payment_intent_id,
                refund_data['id'],
                refund_data['amount'],
                refund_data['status'],
                refund_data['currency']
            )
            patient_url = os.getenv(
                            'NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=payments'

            # Send notifications if enabled
            if notification_check('Refund credit'):
                send_generic_push_notification(
                            user_id=patient.id,
                            title="Refund credited!",
                            body=f'Hi {patient.name}, Your refund for amount {refund_amount} has been credited.',
                            link=patient_url
                        )
          

            if email_check('Refund credit'):
                email_service.refund_email_patient(
                    patient.email,
                    refund_data['amount'],patient.name,doctor.name,appointment,appointment_timings
                    )


            return Response({"status": "refund_processed"}, status=200)

        except Exception as e:
            print(f"Refund processing failed: {str(e)}")
            return Response({"error": str(e)}, status=400)
    # --- DEPRICIATED ---

    def _update_refund_records(self, payment_intent_id, refund_id, amount, status, currency):
        print("this is inside the refund function in webhook")
        defaults = {
            'AmountRefunded': amount,
            'RefundStatus': status,
            'RefundCurrency': currency,
            'RefundDate': timezone.now()
        }

        refund, created = RefundedPayments.objects.update_or_create(
            PaymentIntent=payment_intent_id,
            defaults=defaults
        )

        if not created:
            refund.RefundObject = list(
                set(refund.RefundObject or []) | {refund_id})
            refund.save()

    # --- Beneficiary Handlers ---
    def _handle_beneficiary_created(self, event):
        print(f"Beneficiary created: {event['data']['object']['id']}")
        # Add your beneficiary creation logic here
        return Response({"status": "beneficiary_created"}, status=200)

    def _handle_beneficiary_deleted(self, event):
        print(f"Beneficiary deleted: {event['data']['object']['id']}")
        # Add your beneficiary deletion logic here
        return Response({"status": "beneficiary_deleted"}, status=200)

    # --- Utility Methods ---
    def _get_payment_intent(self, payment_intent_id, retries=3):
        """Fetch payment intent with retry logic"""
        airwallex = AirwallexService()
        for attempt in range(retries):
            try:
                return airwallex.get_payment_intent(payment_intent_id)
            except Exception as e:
                if attempt == retries - 1:
                    raise
                time.sleep(1)



def get_invoices(invoice_id):
    payload = "{}"

    headers = {
        "Content-Type": "application/json",
        "x-api-key": os.getenv("AIRWALLEX_API_KEY"),
        "x-client-id": os.getenv("AIRWALLEX_CLIENT_ID"),
    }

    url1 = f"https://api-demo.airwallex.com/api/v1/authentication/login"

    res = requests.post(url1, json=payload, headers=headers)
    data = res.json()

    print(data)
    AIRWALLEX_API_URL = "https://api-demo.airwallex.com/api/v1/pa/payment_intents"
    # Replace with your demo API key
    AIRWALLEX_API_KEY = os.getenv("AIRWALLEX_API_KEY")

    url = f"{AIRWALLEX_API_URL}/{invoice_id}"
    headers = {
        "Authorization": f"Bearer {data['token']}",
        "Content-Type": "application/json",
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        print(response.json())
        response_data = response.json()
    else:
        print(f"Error {response.status_code}: {response.text}")

    return response


class GetInvoiceView(views.APIView):
    def get(self, r):
        a = get_invoices(r.GET['invoice_id'])
        print(
            f"invoice details:--------------{a}----{type(a)}---{a is not None}")
        return Response(a)


class GetExpertiseView(views.APIView):
    permission_classes = []

    def get(self, r):
        doctor_fees = ExpertiseCancertype.objects.all()
        a = ExpertiseCancertypeSerializer(doctor_fees, many=True)
        return Response(a.data)


# class GetExpertsByFilterView(views.APIView):
#     permission_classes = []

#     def __init__(self):
#         super().__init__()
#         self.semantic_search = SemanticSearch()

#     def get(self, r):
#          # Initial queryset
#         f = CuUser.objects.filter(approval="Approved")
#         f = f.filter(
#             Q(groups__name__in=["researcher", "doctor", "influencer"]) &
#             Q(doctorconsent__Status=1)
#         )

#         # Apply filters
#         for param, value in r.GET.items():
#             if not value:
#                 continue

#             if param == "experts":
#                 f = f.filter(groups__name__exact=value)

#             elif param == "expertise":
#                 matched_specialties = self.semantic_search.find_matching_specialties(value)
#                 if matched_specialties:
#                     f = f.filter(expertise__name__in=matched_specialties)
#                 else:
#                     # Fallback to traditional search
#                     expertise = ExpertiseCancertype.objects.filter(
#                         Q(name__icontains=value) | Q(type__icontains=value)
#                     ).first()
#                     if expertise:
#                         f = f.filter(expertise=expertise.id)

#             elif param == "name":
#                 f = f.filter(name__icontains=value)

#             elif param == "location":
#                 f = f.filter(
#                     Q(City__icontains=value) | Q(Country__iexact=value))

#             elif param == "range":
#                 order_by = (
#                     "doctordetails__ConsultationFees" if value == "asc"
#                     else "-doctordetails__ConsultationFees"
#                 )
#                 f = f.order_by(order_by)

#             elif param == "exp":
#                 f = f.filter(doctordetails__Experience__exact=value)

#             elif param == "review":
#                 if value == "ext":
#                     f = f.filter(doctorreviews__Review__isnull=False)
#                 elif value == "int":
#                     f = f.filter(appointments__patientstories__Rating__isnull=False)
#         # print(f"requestssssssssss{r.GET}")
#         # exp_data = []
#         # f = CuUser.objects.filter(approval="Approved")
#         # y = f.filter(groups__name__exact="researcher")
#         # z = f.filter(groups__name__exact="doctor")
#         # i = f.filter(groups__name__exact="influencer")
#         # f = y | z | i
#         # f = f.filter(doctorconsent__Status__exact=1)
#         # r_review = ""

#         # for x in r.GET:
#         #     if x == "experts" and r.GET["experts"] != "":

#         #         f = f.filter(groups__name__exact=r.GET["experts"])
#         #         print(f"---------------------experts{f}")

#         #     if x == "expertise" and r.GET["expertise"] != "":
#         #         b = ExpertiseCancertype.objects.filter(
#         #             Q(name__icontains=r.GET["expertise"])
#         #             | Q(type__icontains=r.GET["expertise"])
#         #         )
#         #         if b.exists():
#         #             b = b[0].id

#         #             print(f"---------------b {b}")
#         #             f = f.filter(expertise=b)
#         #             print(f"---------------expertise{f}")
#         #         else:
#         #             pass

#         #     elif x == "name" and r.GET["name"] != "":
#         #         f = f.filter(name__icontains=r.GET["name"])

#         #     elif x == "location" and r.GET["location"] != "":
#         #         f = f.filter(
#         #             Q(City__icontains=r.GET["location"])
#         #             | Q(Country__iexact=r.GET["location"])
#         #         )
#         #         print(f"---------------------location{f}")

#         #     elif x == "range" and r.GET["range"] != "":
#         #         if r.GET["range"] == "asc":
#         #             f = f.order_by("doctordetails__ConsultationFees")
#         #         elif r.GET["range"] == "desc":
#         #             f = f.order_by("-doctordetails__ConsultationFees")

#         #         else:
#         #             print({"message": "invalid price filter"})

#         #         print(f"---------------------range{f}")

#         #     elif x == "exp" and r.GET["exp"] != "":

#         #         f = f.filter(doctordetails__Experience__exact=r.GET["exp"])
#         #         print(f"---------------------exp{f}")

#         #     elif x == "review" and r.GET["review"] != "":
#         #         if r.GET["review"] == "ext":
#         #             f = f.filter(doctorreviews__Review__isnull=False)
#         #             print(f"---------review------------{f}--------------------")
#         #         elif r.GET["review"] == "int":
#         #             f = f.filter(appointments__patientstories__Rating__isnull=False)
#         #             print(f"---------review------------{f}--------------------")
#         #         else:
#         #             print({"message": "invalid review filter"})

#         #     else:

#         #         print({"message": "invalid filter"})
#         # Get the page number from the request
#         page_number = r.GET.get("page", 1)
#         # Get the number of items per page from the request
#         items_per_page = r.GET.get("per_page", 10)
#         paginator = Paginator(f, items_per_page)
#         total_items = f.count()
#         if int(page_number) not in range(1, int(paginator.num_pages) + 1):
#             return HttpResponse("Not a valid page number", status=400)
#         f = paginator.page(page_number)
#         for x in f:
#             user_data = filter_user_data(get_user_model().objects.get(id__exact=x.id))

#             print(f"-------------------user_data-------------other------{user_data}")

#             l1 = dict()
#             if r_review == "int":
#                 print(f"-------------------review")
#             elif r_review == "ext":
#                 print(f"-------------------review")
#             else:
#                 pass

#             l1.update(
#                 {
#                     "expert_details": CuUserSerializer(x).data,
#                     "expert_role": get_cu_user_type(x.id),
#                 }
#             )
#             exp_rank = ExpertRank.objects.filter(ExpertId_id__exact=x.id)
#             if exp_rank.exists():
#                 l1.update({"expert_rank": exp_rank[0].rank})
#             else:
#                 l1.update({"expert_rank": None})

#             exp_ask = RandomExpert.objects.filter(ExpertId_id__exact=x.id)
#             if exp_ask.exists():
#                 l1.update({"expert_recommended": True})
#             else:
#                 l1.update({"expert_recommended": False})
#             # print(f'----------checking something-----{CuUserSerializer(x).data}')
#             l1.update(
#                 {"expert_other_details": DoctorDetailsSerializer(x.doctordetails).data}
#             )
#             # added ratings data for experts
#             if l1["expert_other_details"]["ProfilePhoto"] is not None:
#                 new_obj_url = get_s3_signed_url_bykey(
#                     l1["expert_other_details"]["ProfilePhoto"]
#                 )
#                 l1["expert_other_details"]["ProfilePhoto"] = new_obj_url

#             if l1["expert_other_details"]["Signature"] is not None:
#                 new_obj_url = get_s3_signed_url_bykey(
#                     l1["expert_other_details"]["Signature"]
#                 )
#                 l1["expert_other_details"]["Signature"] = new_obj_url

#             if l1["expert_other_details"]["IntVideoUrl"] is not None:
#                 a_i = []
#                 b = l1["expert_other_details"]["IntVideoUrl"]
#                 for i in b:
#                     new_obj_url = get_s3_signed_url_bykey(i)
#                     a_i.append(new_obj_url)
#                     print(f"ddd details video--------{new_obj_url}")
#                 l1["expert_other_details"]["IntVideoUrl"] = a_i

#             Apps = Appointments.objects.filter(slot_id__doctor__exact=x.id)
#             doctors_s = CuUser.objects.filter(id__exact=x.id)
#             stories_count = 0
#             reviews_count = 0
#             rating_s = 0
#             rating_r = 0
#             for z in Apps:
#                 stories = z.patientstories_set
#                 stories_count += stories.count()
#                 for v in stories.all():
#                     rating_s += v.Rating
#             for z in doctors_s:
#                 reviews = z.doctorreviews_set
#                 for u in reviews.all():
#                     if u.ReviewStatus == 2:
#                         rating_r += u.ReviewRating
#                         reviews_count += 1
#             rating_in_number1 = (
#                 int(rating_s / stories_count) if stories_count != 0 else 0
#             )
#             rating_in_number2 = (
#                 int(rating_r / reviews_count) if reviews_count != 0 else 0
#             )
#             rating_in_number = int((rating_in_number1 + rating_in_number2) / 2)
#             # print(f"appssssssssssssssss{stories_count}------------{rating}-------{x.id}----------{Apps}---")
#             l1.update(
#                 {
#                     "patient_stories": stories_count,
#                     "doctor_reviews": reviews_count,
#                     "rating": rating_in_number,
#                 }
#             )
#             # code ends here
#             exp_data.append(l1)

#         response_data = {
#             "total_items": total_items,
#             "total_pages": paginator.num_pages,
#             "experts_data": exp_data,
#         }
#         return JsonResponse(response_data)


class GetExpertsByFilterView(views.APIView):
    permission_classes = []

    def get(self, request):
        # Initial queryset
        f = CuUser.objects.filter(approval="Approved")
        f = f.filter(
            Q(groups__name__in=["researcher", "doctor", "influencer"]) &
            Q(doctorconsent__Status=1)
        )

        # Apply filters
        for param, value in request.GET.items():
            if not value:
                continue

            if param == "experts":
                f = f.filter(groups__name__exact=value)

            elif param == "expertise" and request.GET["expertise"] != "":
                b = ExpertiseCancertype.objects.filter(
                    Q(name__icontains=request.GET["expertise"])
                    | Q(type__icontains=request.GET["expertise"])
                )
                if b.exists():
                    b = b[0].id

                    print(f"---------------b {b}")
                    f = f.filter(expertise=b)
                    print(f"---------------expertise{f}")
                else:
                    pass

            elif param == "name":
                f = f.filter(name__icontains=value)

            elif param == "location":
                f = f.filter(
                    Q(City__icontains=value) | Q(Country__iexact=value))

            elif param == "range":
                order_by = (
                    "doctordetails__ConsultationFees" if value == "asc"
                    else "-doctordetails__ConsultationFees"
                )
                f = f.order_by(order_by)

            elif param == "exp":
                f = f.filter(doctordetails__Experience__exact=value)

            elif param == "review":
                if value == "ext":
                    f = f.filter(doctorreviews__Review__isnull=False)
                elif value == "int":
                    f = f.filter(
                        appointments__patientstories__Rating__isnull=False)

        # Pagination
        page_number = request.GET.get("page", 1)
        items_per_page = request.GET.get("per_page", 10)
        paginator = Paginator(f, items_per_page)

        try:
            page = paginator.page(page_number)
        except:
            return Response({"error": "Invalid page number"}, status=400)

        # Prepare response
        exp_data = []
        for expert in page:
            expert_data = self._prepare_expert_data(expert)
            exp_data.append(expert_data)

        return JsonResponse({
            "total_items": paginator.count,
            "total_pages": paginator.num_pages,
            "experts_data": exp_data,
        })

    def _prepare_expert_data(self, expert):
        """Helper method to prepare expert data"""
        data = {
            "expert_details": CuUserSerializer(expert).data,
            "expert_role": get_cu_user_type(expert.id),
            "expert_rank": self._get_expert_rank(expert.id),
            "expert_recommended": RandomExpert.objects.filter(ExpertId_id=expert.id).exists(),
            "expert_other_details": DoctorDetailsSerializer(expert.doctordetails).data,
        }

        # Process media URLs
        self._process_media_urls(data["expert_other_details"])

        # Calculate ratings
        ratings = self._calculate_ratings(expert)
        data.update(ratings)

        return data

    def _get_expert_rank(self, expert_id):
        rank = ExpertRank.objects.filter(ExpertId_id=expert_id).first()
        return rank.rank if rank else None

    def _process_media_urls(self, details):
        """Process all media URLs in expert details"""
        for field in ["ProfilePhoto", "Signature"]:
            if details[field]:
                details[field] = get_s3_signed_url_bykey(details[field])

        if details["IntVideoUrl"]:
            details["IntVideoUrl"] = [
                get_s3_signed_url_bykey(url)
                for url in details["IntVideoUrl"]
            ]

    def _calculate_ratings(self, expert):
        """Calculate all rating-related metrics"""
        apps = Appointments.objects.filter(slot_id__doctor=expert.id)
        stories = [
            story for app in apps for story in app.patientstories_set.all()]
        reviews = expert.doctorreviews_set.filter(ReviewStatus=2)

        stories_count = len(stories)
        reviews_count = reviews.count()

        rating_s = sum(story.Rating for story in stories) if stories else 0
        rating_r = sum(
            review.ReviewRating for review in reviews) if reviews else 0

        rating_in_number1 = rating_s // stories_count if stories_count else 0
        rating_in_number2 = rating_r // reviews_count if reviews_count else 0

        avg_rating = (rating_in_number1 +
                      rating_in_number2) // 2 if (stories_count + reviews_count) else 0

        return {
            "patient_stories": stories_count,
            "doctor_reviews": reviews_count,
            "rating": avg_rating,
        }

# added


class GetReportTypeView(generics.RetrieveAPIView):
    serializer_class = ReportTypeSerializer
    queryset = ReportType.objects.all()
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        if self.kwargs["id"] == "all":
            r_t = ReportType.objects.all()
            rt = [serialize_model(t, ReportTypeSerializer) for t in r_t]
            return JsonResponse(rt, safe=False)
        a = self.retrieve(request, *args, **kwargs)
        return a


# added


# added public api view
class GetAppPolicyContentTypeView(views.APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        r_t = AdminContentManagement.objects.filter(
            Category__exact="Reschedule")
        rt = [serialize_model(t, ContentTypeSerializer) for t in r_t]
        c_t = AdminContentManagement.objects.filter(
            Category__exact="Cancellation Refund Policy"
        )
        ct = [serialize_model(t, ContentTypeSerializer) for t in c_t]

        return JsonResponse({"Reschedule policy": rt, "Cancellation Refund Policy": ct})


# added for patient refund views
class GetPatientRefundView(views.APIView):

    def get(self, r, *args, **kwargs):
        if "user_id" in r.GET:
            if get_cu_user_type(r.GET["user_id"]) in ["admin", "child_admin"]:
                pass
            else:
                return HttpResponse(
                    "You are not authorized to perforn this action.", status=401
                )

        airwallex = AirwallexService()

        def update_refund_status(refund_obj):
            try:
                if refund_obj.RefundObject:
                    airwallex_refund = airwallex.get_refund(
                        refund_obj.RefundId)
                    latest_status = airwallex_refund.get("status")
                    if latest_status and latest_status != refund_obj.RefundStatus:
                        refund_obj.RefundStatus = latest_status
                        refund_obj.save(update_fields=["RefundStatus"])
            except Exception:
                pass  # silent failure

        refund_apps = RefundedPayments.objects.all()
        refund_apps_ids = refund_apps.values_list("AppId_id")
        refund_data = []
        app_data = []
        if self.kwargs.get("patient_id") == "all":
            app_data = Appointments.objects.filter(id__in=refund_apps_ids)
        else:
            app_data = Appointments.objects.filter(
                id__in=refund_apps_ids, patient_id__exact=self.kwargs["patient_id"]
            )
        if "id" in r.GET and r.GET["id"] != "":
            app_data = app_data.filter(id__exact=r.GET["id"])
        else:
            pass
        if "name" in r.GET and r.GET["name"] != "":
            app_data = app_data.filter(patient__name__icontains=r.GET["name"])
        app_data = app_data.order_by("-id")
        total_items = app_data.count()
        # Get the page number from the request
        page_number = r.GET.get("page", 1)
        # Get the number of items per page from the request
        items_per_page = r.GET.get("per_page", 10)
        paginator = Paginator(app_data, items_per_page)
        if int(page_number) not in range(1, int(paginator.num_pages) + 1):
            return HttpResponse("Not a valid page number", status=400)
        app_data = paginator.page(page_number)

        refund_data = []
        for x in app_data:
            try:
                refund_obj = RefundedPayments.objects.get(AppId_id=x.id)
                # ✅ Update from Airwallex if needed
                update_refund_status(refund_obj)

                refund_data.append({
                    "Patient name": x.patient.name,
                    "Appointment ID": x.id,
                    "Currency": refund_obj.RefundCurrency,
                    "Refunded amount": refund_obj.AmountRefunded,
                    "RefundStatus": refund_obj.RefundStatus,
                    "RefundObject": refund_obj.RefundObject,
                    "Refund Date": timezone.localtime(refund_obj.RefundDate),
                })
            except Exception:
                continue  # skip silently on any error

        # for x in app_data:
        #     l1 = dict()
        #     r_obj = RefundedPayments.objects.get(AppId_id__exact=x.id)
        #     update_refund_status(r_obj)
        #     l1.update(
        #         {
        #             "Patient name": x.patient.name,
        #             "Appointment ID": x.id,
        #             "Currency": r_obj.RefundCurrency,
        #             "Refunded amount": r_obj.AmountRefunded,
        #             "RefundStatus": r_obj.RefundStatus,
        #             "RefundObject": r_obj.RefundObject,
        #             "Refund Date": timezone.localtime(r_obj.RefundDate),
        #             # "Refund Date": timezone.localtime(r_obj.RefundDate).date(),
        #         }
        #     )
        #     refund_data.append(l1)
        return Response(
            {
                "total_items": total_items,
                "total_pages": paginator.num_pages,
                "refund_data": refund_data,
            }
        )


# --------------update_banner-------------------------
class GetBanner(views.APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        a = UpdateBanner.objects.all().order_by("-addedTime")
        a = a[0]
        a = serialize_model(a, UpdateBannerSerializer)
        return Response(a)


class DoctorSearchView(APIView):
    permission_classes = []

    def get(self, request):
        # query = request.data.get('query', '').strip()
        query = request.query_params.get('query', '').strip()

        base_queryset = CuUser.objects.filter(
            approval="Approved",
            embedding__embedding__isnull=False,
            groups__name__in=["researcher", "doctor", "influencer"],
            doctorconsent__Status=1
        ).prefetch_related(
            Prefetch('expertise', queryset=ExpertiseCancertype.objects.all()),
            Prefetch('embedding', queryset=Embedding.objects.filter(
                embedding_type='doctor_profile')),
            Prefetch('doctordetails')
        ).distinct()

        try:
            if query:
                # Cache check
                cache_key = f"doctor_search_{hash(query)}"
                cached_result = cache.get(cache_key)
                if cached_result:
                    return Response(cached_result)

                # Semantic embedding
                query_embedding = get_embedding(query)
                if query_embedding is None:
                    return Response({"error": "Failed to process query"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                specialties = ExpertiseCancertype.objects.exclude(
                    embedding__isnull=True)
                if not specialties.exists():
                    return Response({"error": "No specialties available"}, status=status.HTTP_404_NOT_FOUND)

                specialty_embeddings = [
                    np.array(s.embedding) for s in specialties]
                specialty_names = [s.name for s in specialties]

                similarities = cosine_similarity(query_embedding.reshape(
                    1, -1), np.array(specialty_embeddings))[0]

                similarity_threshold = 0.77
                related_specialties = [
                    specialty_names[i] for i, score in enumerate(similarities)
                    if score > similarity_threshold
                ]

                if not related_specialties:
                    return Response({"error": "No relevant specialties found"}, status=status.HTTP_404_NOT_FOUND)

                related_specialty_objects = ExpertiseCancertype.objects.filter(
                    name__in=related_specialties)
                base_queryset = base_queryset.filter(
                    expertise__in=related_specialty_objects)

            else:
                related_specialties = []

            # Apply filters (all optional)
            filters = request.query_params
            for param, value in filters.items():
                if not value:
                    continue
                if param == "experts":
                    base_queryset = base_queryset.filter(
                        groups__name__exact=value)
                elif param == "name":
                    base_queryset = base_queryset.filter(name__icontains=value)
                elif param == "location":
                    base_queryset = base_queryset.filter(
                        Q(City__icontains=value) | Q(Country__iexact=value)
                    )
                elif param == "range":
                    order_by = (
                        "doctordetails__ConsultationFees" if value == "asc"
                        else "-doctordetails__ConsultationFees"
                    )
                    base_queryset = base_queryset.order_by(order_by)
                elif param == "exp":
                    base_queryset = base_queryset.filter(
                        doctordetails__Experience__exact=value)
                elif param == "review":
                    if value == "ext":
                        base_queryset = base_queryset.filter(
                            doctorreviews__Review__isnull=False)
                    elif value == "int":
                        base_queryset = base_queryset.filter(
                            appointments__patientstories__Rating__isnull=False)

            if not base_queryset.exists():
                return Response({"message": "No doctors found"}, status=status.HTTP_200_OK)

            results = []
            if query:
                doctor_embeddings = [
                    np.array(d.embedding.embedding) for d in base_queryset]
                doctor_similarities = cosine_similarity(
                    query_embedding.reshape(1, -1), np.array(doctor_embeddings))[0]

                for idx, doctor in enumerate(base_queryset):
                    if doctor_similarities[idx] < 0.7:
                        continue
                    matching_specialties = [
                        spec.name for spec in doctor.expertise.all() if spec.name in related_specialties
                    ]
                    if not matching_specialties:
                        continue

                    doctor_data = build_detailed_doctor_data(doctor)
                    doctor_data["similarity_score"] = float(
                        doctor_similarities[idx])
                    results.append(doctor_data)

                results.sort(key=lambda x: x['similarity_score'], reverse=True)
            else:
                for doctor in base_queryset:
                    doctor_data = build_detailed_doctor_data(doctor)
                    results.append(doctor_data)

            # Pagination
            page_number = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get("per_page", 10))
            paginator = Paginator(results, per_page)
            paginated_results = paginator.get_page(page_number)

            response_data = {
                "query": query,
                "related_specialties": related_specialties,
                "total_results": paginator.count,
                "total_pages": paginator.num_pages,
                "current_page": paginated_results.number,
                "results": list(paginated_results)
            }

            if query:
                cache.set(cache_key, response_data, timeout=3600)

            return Response(response_data)

        except Exception as e:
            print(f"Search error: {e}")
            return Response({"error": "An unexpected error occurred"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PrecomputeEmbeddingsView(APIView):
    permission_classes = []

    def post(self, request):
        """Admin endpoint to precompute embeddings for specialties and doctors in batches."""
        try:
            # Batch process specialties
            specialties = ExpertiseCancertype.objects.all()
            specialty_texts = [(s, s.name)
                               for s in specialties if not s.embedding]
            for specialty, text in specialty_texts:
                embedding = get_embedding(text)
                if embedding is not None:
                    specialty.embedding = embedding.tolist()
                    specialty.save()

            # Batch process doctors
            doctors = CuUser.objects.filter(
                approval="Approved",
                groups__name__in=["researcher", "doctor", "influencer"],
                doctorconsent__Status=1
            ).prefetch_related('expertise', 'doctordetails')

            for doctor in doctors:
                if not hasattr(doctor, 'embedding') or not doctor.embedding.embedding:
                    # Create a comprehensive doctor profile
                    specialties_text = ", ".join(
                        [s.name for s in doctor.expertise.all()])
                    # Include bio from DoctorDetails if available
                    bio = doctor.doctordetails.Summary if hasattr(
                        doctor, 'doctordetails') and doctor.doctordetails.Summary else ""
                    doctor_text = (
                        f"Name: {doctor.name}. "
                        f"Specialties: {specialties_text}. "
                        f"Bio: {bio}"
                    )
                    # doctor_text = f"Name: {doctor.name}. Specialties: {specialties_text}"
                    embedding = get_embedding(doctor_text)
                    if embedding is not None:
                        Embedding.objects.update_or_create(
                            user=doctor,
                            defaults={'embedding': embedding.tolist(
                            ), 'embedding_type': 'doctor_profile'}
                        )

            return Response({"status": "Embeddings computed successfully"})
        except Exception as e:
            print(f"Precompute error: {e}")
            return Response({"error": "Failed to compute embeddings"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
