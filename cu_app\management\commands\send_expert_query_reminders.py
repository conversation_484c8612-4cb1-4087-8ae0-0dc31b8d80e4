# management/commands/send_expert_query_reminders.py
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from cu_app.utils.helper_functions import *
from cu_admin.user_notifications import *
import os
from cu_app.services.email_services import EmailService
import logging
from cu_app.models.patient_models import *
from django.db.models import Q

logger = logging.getLogger(__name__)

email_service = EmailService()


class Command(BaseCommand):
    help = 'Sends reminders to experts about unanswered patient queries'

    def handle(self, *args, **options):

        try:
            # Get all unanswered queries (assuming 'status' field exists)
            unanswered_queries = PatientQueries.objects.filter(
                Q(status='P')
            ).select_related('ApptId__slot_id__doctor', 'ApptId__patient')

            now = timezone.now()
            processed_counts = {
                'first': 0,
                'second': 0,
                'final': 0
            }

            for query in unanswered_queries:
                if not query.ApptId or not query.ApptId.slot_id:
                    continue
                query_age = now - query.QueryTime  # Using your QueryTime field
                expert = query.ApptId.slot_id.doctor
                patient = query.ApptId.patient

                # First reminder (24 hours)
                if query_age >= timedelta(hours=24) and not query.first_reminder_sent:
                    self._send_expert_reminder(
                        expert=expert,
                        patient=patient,
                        query=query,
                        reminder_type='first',
                        hours_passed=24
                    )
                    query.first_reminder_sent = now
                    processed_counts['first'] += 1

                # Second reminder (48 hours)
                if query_age >= timedelta(minutes=28) and not query.second_reminder_sent:
                    # if query_age >= timedelta(hours=48) and not query.second_reminder_sent:
                    self._send_expert_reminder(
                        expert=expert,
                        patient=patient,
                        query=query,
                        reminder_type='second',
                        hours_passed=48
                    )
                    query.second_reminder_sent = now
                    processed_counts['second'] += 1

                # Final reminder (70 hours - 2 hours before deadline)
                if query_age >= timedelta(hours=70) and not query.final_reminder_sent:
                    self._send_expert_reminder(
                        expert=expert,
                        patient=patient,
                        query=query,
                        reminder_type='final',
                        hours_passed=70
                    )
                    query.final_reminder_sent = now
                    processed_counts['final'] += 1

                query.save()

            logger.info(
                f"Time now: {timezone.now()}, "
                f"Expert reminders sent - First: {processed_counts['first']}, "
                f"Second: {processed_counts['second']}, "
                f"Final: {processed_counts['final']}"
            )
            self.stdout.write(self.style.SUCCESS(
                f"Sent reminders - First: {processed_counts['first']}, "
                f"Second: {processed_counts['second']}, "
                f"Final: {processed_counts['final']}"
            ))

        except Exception as e:
            logger.error(
                f"Error in expert reminder job: {str(e)}", exc_info=True)
            self.stdout.write(self.style.ERROR(f'Error: {str(e)}'))

    def _send_expert_reminder(self, expert, patient, query, reminder_type, hours_passed):
        """Handles sending reminders to experts"""
        try:
            expert_url = os.getenv(
                "NEXT_CANCER_UNWIRED_DOCTOR_APP", "") + "/payments"
            remaining_time = 72 - hours_passed

            # Notification
            if notification_check("Expert Query Reminder"):
                send_generic_push_notification(
                    user_id=expert.id,
                    title=f"Patient Query Reminder ({reminder_type})",
                    body=f"Patient {patient.name}'s query from {query.QueryTime.strftime('%b %d')} is waiting. {remaining_time}h remaining",
                    link=expert_url
                )

            # Email
            if email_check("Expert Query Reminder"):
                email_service.send_expert_query_reminder(
                    expert_email=expert.email,
                    expert_name=expert.name,
                    patient_name=patient.name,
                    query_created=query.QueryTime.strftime(
                        '%b %d, %Y %I:%M %p'),
                    query_text=query.PQuery[:100] +
                    '...' if query.PQuery else '[No text provided]',
                    remaining_hours=remaining_time,
                    query_url=f"{expert_url}?query_id={query.id}",
                    reminder_type=reminder_type
                )

            logger.info(
                f"Sent {reminder_type} reminder to expert {expert.id} for query {query.id}")

        except Exception as e:
            logger.error(
                f"Failed to send {reminder_type} reminder to expert {expert.id} "
                f"for query {query.id}: {str(e)}",
                exc_info=True
            )
            raise
